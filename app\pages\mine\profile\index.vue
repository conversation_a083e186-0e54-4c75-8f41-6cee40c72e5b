<template>
  <view class="profile-container">
    <view class="avatar-section">
      <!-- 头像显示区域 -->
      <view @tap="chooseAvatarImage" class="avatar-button">
        <image :src="userInfo.avatar || defaultAvatar" class="avatar"></image>

        <!-- 微信头像选择按钮覆盖层 -->
        <button
          v-if="showWechatAvatarOverlay"
          open-type="chooseAvatar"
          @chooseavatar="onChooseWechatAvatar"
          @error="onChooseAvatarError"
          class="avatar-overlay-button">
          选择微信头像
        </button>
      </view>

      <text class="tip">点击更换头像</text>
    </view>
    
    <view class="form-section">
      <view class="form-item">
        <text class="label">昵称</text>
        <input type="nickname" @change="onInputNickname" :value="userInfo.nickname" placeholder="请输入您的昵称" />
      </view>
      
      <button @click="saveProfile" class="save-btn cu-btn block bg-green lg">保存</button>
    </view>
  </view>
</template>

<script>
import { updateUserInfo } from '@/api/auth'
import { getToken } from '@/utils/auth'
import config from '@/config'

export default {
  data() {
    return {
      userInfo: {
        avatar: '',
        nickname: ''
      },
      defaultAvatar: '/static/images/default-avatar.png',
      isFirstLogin: false,
      baseUrl: config.baseUrl || 'http://localhost:8080',
      showWechatAvatarButton: false, // 控制是否显示微信头像按钮
      showWechatAvatarOverlay: false // 控制头像覆盖层显示
    }
  },
  onLoad(options) {
    // 获取当前用户信息
    const userInfo = uni.getStorageSync('userInfo') || {}
    console.log('从存储获取的用户信息:', userInfo);

    this.userInfo = {
      ...userInfo,
      nickname: userInfo.nickName || userInfo.nickname || '',
      avatar: userInfo.avatarUrl || userInfo.avatar || userInfo.avatar_url || ''
    }

    console.log('处理后的用户信息:', this.userInfo);

    // 检查是否首次登录
    this.isFirstLogin = options.first === '1'

    // 检查当前环境是否支持 chooseAvatar
    this.checkWechatAvatarSupport()
  },
  methods: {
    // 检查微信头像支持
    checkWechatAvatarSupport() {
      // 检查是否在真机环境且支持 chooseAvatar
      // #ifdef MP-WEIXIN
      const systemInfo = uni.getSystemInfoSync()
      console.log('系统信息:', systemInfo);

      // 在真机上或者较新版本的开发者工具中启用
      if (systemInfo.platform !== 'devtools' || systemInfo.SDKVersion >= '2.21.2') {
        this.showWechatAvatarButton = false // 暂时禁用，避免ENOENT错误
      }
      // #endif
    },

    // 微信头像选择回调
    onChooseWechatAvatar(e) {
      console.log('微信头像选择回调:', e.detail);
      const { avatarUrl } = e.detail

      if (avatarUrl) {
        this.userInfo.avatar = avatarUrl
        this.downloadAndUploadWechatAvatar(avatarUrl)
      } else {
        uni.showToast({ title: '获取微信头像失败', icon: 'none' })
      }
    },

    // 选择头像 - 提供多种选项
    chooseAvatarImage() {
      console.log('开始选择头像');

      // 根据环境提供不同选项
      const itemList = []
      const systemInfo = uni.getSystemInfoSync()

      // 如果支持微信头像，添加到第一位
      if (this.canUseWechatAvatar()) {
        itemList.push('用微信头像')
      }

      // 添加基础选项
      itemList.push('从相册选择', '拍照')

      uni.showActionSheet({
        itemList: itemList,
        success: (res) => {
          const selectedOption = itemList[res.tapIndex]
          console.log('用户选择了:', selectedOption);

          if (selectedOption.includes('用微信头像')) {
            this.chooseRealWechatAvatar()
          } else if (selectedOption === '从相册选择') {
            this.chooseFromAlbum()
          } else if (selectedOption === '拍照') {
            this.chooseFromCamera()
          }
        },
        fail: (err) => {
          console.log('用户取消选择');
        }
      })
    },

    // 选择真实的微信头像
    chooseRealWechatAvatar() {
      console.log('选择真实的微信头像');

      // 检查环境和版本支持
      const systemInfo = uni.getSystemInfoSync()
      console.log('系统信息:', systemInfo);

      // 直接显示微信头像选择按钮
      this.showWechatAvatarOverlay = true

      // 显示提示
      uni.showToast({
        title: '请点击头像选择微信头像',
        icon: 'none',
        duration: 2000
      })

      // 10秒后自动隐藏覆盖层（给用户足够时间）
      setTimeout(() => {
        if (this.showWechatAvatarOverlay) {
          this.showWechatAvatarOverlay = false
          console.log('覆盖层自动隐藏');
        }
      }, 10000)
    },

    // 检查是否可以使用微信头像
    canUseWechatAvatar() {
      // #ifdef MP-WEIXIN
      // 在微信小程序环境中总是显示微信头像选项
      return true
      // #endif

      // #ifndef MP-WEIXIN
      return false
      // #endif
    },



    // 处理微信头像选择回调
    onChooseWechatAvatar(e) {
      console.log('微信头像选择回调:', e);

      // 隐藏覆盖层
      this.showWechatAvatarOverlay = false

      const { avatarUrl } = e.detail
      console.log('获取到的微信头像URL:', avatarUrl);

      if (avatarUrl && avatarUrl.trim() !== '') {
        // 立即显示选择的头像
        this.userInfo.avatar = avatarUrl

        // 直接更新用户信息，不进行复杂的上传处理
        // 因为微信头像URL可以直接使用
        this.updateUserAvatar(avatarUrl)

        uni.showToast({
          title: '头像设置成功',
          icon: 'success'
        })
      } else {
        console.error('未获取到有效的头像URL');
        uni.showToast({
          title: '未获取到头像，请重试',
          icon: 'none'
        })
      }
    },

    // 处理微信头像选择错误
    onChooseAvatarError(e) {
      console.log('微信头像选择错误 (可忽略的开发者工具问题):', e);

      // 隐藏覆盖层
      this.showWechatAvatarOverlay = false

      // 检查是否是开发者工具的ENOENT错误
      const errorMsg = e.detail?.errMsg || e.errMsg || ''
      if (errorMsg.includes('ENOENT') || errorMsg.includes('no such file')) {
        console.log('这是开发者工具的已知问题，不影响功能');

        // 不显示错误提示，因为这是开发者工具的问题
        // 在真机上不会出现此错误
        return
      }

      // 其他错误才显示提示
      uni.showToast({
        title: '头像选择失败，请重试',
        icon: 'none'
      })
    },



    // 处理选择的微信头像
    handleChosenWechatAvatar(avatarUrl) {
      console.log('处理选择的微信头像:', avatarUrl);

      // 检查URL是否有效
      if (!avatarUrl || avatarUrl.trim() === '') {
        uni.hideLoading()
        uni.showToast({
          title: '头像URL无效',
          icon: 'none'
        })
        return
      }

      // 检查环境决定处理方式
      const systemInfo = uni.getSystemInfoSync()

      if (systemInfo.platform === 'devtools') {
        // 开发者工具：直接使用URL，不下载
        console.log('开发者工具环境 - 直接使用头像URL');
        this.uploadWechatAvatarUrl(avatarUrl)
      } else {
        // 真机：尝试下载后上传
        console.log('真机环境 - 尝试下载后上传');
        this.downloadWechatAvatarOnDevice(avatarUrl)
      }
    },

    // 在开发者工具中获取微信头像
    getWechatAvatarInDevtools() {
      console.log('开发者工具环境 - 使用安全的头像获取方式');

      // 方法1: 尝试 getUserProfile
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('getUserProfile成功:', res);
          const avatarUrl = res.userInfo.avatarUrl

          if (avatarUrl && avatarUrl !== '') {
            this.userInfo.avatar = avatarUrl
            this.downloadAndUploadWechatAvatar(avatarUrl)
          } else {
            this.tryGetStoredUserInfo()
          }
        },
        fail: (err) => {
          console.log('getUserProfile失败:', err);
          this.tryGetStoredUserInfo()
        }
      })
    },

    // 尝试获取已存储的用户信息
    tryGetStoredUserInfo() {
      console.log('尝试获取已存储的用户信息');

      // 从本地存储获取用户信息
      const storedUserInfo = uni.getStorageSync('userInfo')
      if (storedUserInfo && storedUserInfo.avatarUrl) {
        console.log('使用已存储的头像:', storedUserInfo.avatarUrl);
        this.userInfo.avatar = storedUserInfo.avatarUrl
        this.downloadAndUploadWechatAvatar(storedUserInfo.avatarUrl)
        return
      }

      // 如果没有存储的头像，提供模拟数据用于开发测试
      uni.showModal({
        title: '开发者工具提示',
        content: '在开发者工具中无法获取真实微信头像，是否使用测试头像？',
        success: (res) => {
          if (res.confirm) {
            // 使用一个测试头像URL
            const testAvatarUrl = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'
            this.userInfo.avatar = testAvatarUrl
            this.downloadAndUploadWechatAvatar(testAvatarUrl)
          } else {
            this.fallbackToAlbum()
          }
        }
      })
    },

    // 在真机上获取微信头像
    getWechatAvatarOnDevice() {
      console.log('真机环境 - 使用完整的头像获取功能');

      // 方法1: 优先使用 getUserProfile
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('getUserProfile成功:', res);
          const avatarUrl = res.userInfo.avatarUrl

          if (avatarUrl && avatarUrl !== '') {
            this.userInfo.avatar = avatarUrl
            this.downloadAndUploadWechatAvatar(avatarUrl)
          } else {
            this.tryChooseAvatarOnDevice()
          }
        },
        fail: (err) => {
          console.log('getUserProfile失败，尝试其他方法:', err);
          this.tryChooseAvatarOnDevice()
        }
      })
    },

    // 在真机上尝试使用 chooseAvatar
    tryChooseAvatarOnDevice() {
      console.log('真机环境 - 尝试使用chooseAvatar');

      // 创建一个临时的 chooseAvatar 按钮
      // 注意：这里我们不直接使用模板中的按钮，而是通过编程方式触发
      uni.showModal({
        title: '获取微信头像',
        content: '请点击确定后选择微信头像',
        success: (res) => {
          if (res.confirm) {
            // 这里可以触发隐藏的 chooseAvatar 按钮
            // 或者降级到其他方案
            this.fallbackToAlbum()
          }
        }
      })
    },



    // 降级到相册选择
    fallbackToAlbum() {
      uni.showModal({
        title: '提示',
        content: '无法获取微信头像，是否从相册选择？',
        success: (res) => {
          if (res.confirm) {
            this.chooseFromAlbum()
          }
        }
      })
    },

    // 从相册选择
    chooseFromAlbum() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album'],
        success: (res) => {
          console.log('从相册选择成功:', res);
          const tempFilePath = res.tempFilePaths[0]
          this.userInfo.avatar = tempFilePath
          this.uploadAvatar(tempFilePath)
        },
        fail: (err) => {
          console.error('从相册选择失败:', err);
          uni.showToast({ title: '选择图片失败', icon: 'none' })
        }
      })
    },

    // 拍照
    chooseFromCamera() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera'],
        success: (res) => {
          console.log('拍照成功:', res);
          const tempFilePath = res.tempFilePaths[0]
          this.userInfo.avatar = tempFilePath
          this.uploadAvatar(tempFilePath)
        },
        fail: (err) => {
          console.error('拍照失败:', err);
          uni.showToast({ title: '拍照失败', icon: 'none' })
        }
      })
    },

    // 下载并上传微信头像
    downloadAndUploadWechatAvatar(avatarUrl) {
      console.log('开始处理微信头像:', avatarUrl);

      // 检查运行环境
      const systemInfo = uni.getSystemInfoSync()

      if (systemInfo.platform === 'devtools') {
        // 开发者工具环境：直接使用头像URL，不下载
        this.handleWechatAvatarInDevtools(avatarUrl)
      } else {
        // 真机环境：正常下载流程
        this.downloadWechatAvatarOnDevice(avatarUrl)
      }
    },

    // 开发者工具中处理微信头像
    handleWechatAvatarInDevtools(avatarUrl) {
      console.log('开发者工具环境 - 直接使用头像URL');
      uni.showLoading({ title: '处理中...' })

      // 在开发者工具中，我们直接将微信头像URL发送给后端
      // 让后端来下载和处理头像
      this.uploadWechatAvatarUrl(avatarUrl)
    },

    // 真机环境下载微信头像
    downloadWechatAvatarOnDevice(avatarUrl) {
      console.log('真机环境 - 下载微信头像:', avatarUrl);
      uni.showLoading({ title: '下载中...' })

      // 在真机上正常下载微信头像
      uni.downloadFile({
        url: avatarUrl,
        success: (res) => {
          console.log('下载微信头像成功:', res);
          if (res.statusCode === 200) {
            // 上传下载的头像文件
            this.uploadAvatar(res.tempFilePath)
          } else {
            uni.hideLoading()
            uni.showToast({ title: '下载头像失败', icon: 'none' })
          }
        },
        fail: (err) => {
          console.error('下载微信头像失败:', err);
          uni.hideLoading()
          uni.showToast({ title: '下载头像失败', icon: 'none' })
        }
      })
    },

    // 上传微信头像URL（开发者工具专用）
    uploadWechatAvatarUrl(avatarUrl) {
      console.log('直接使用微信头像URL:', avatarUrl);

      // 在开发者工具中，直接使用微信头像URL
      // 不需要上传到后端，直接更新用户信息
      this.userInfo.avatar = avatarUrl

      // 更新用户信息到后端
      this.updateUserAvatar(avatarUrl)

      uni.hideLoading()
      uni.showToast({
        title: '头像设置成功',
        icon: 'success'
      })
    },
    
    // 输入昵称回调
    onInputNickname(e) {
      this.userInfo.nickname = e.detail.value
    },
    
    // 上传头像
    uploadAvatar(filePath) {
      console.log('开始上传头像:', filePath);

      // 检查文件路径是否有效
      if (!filePath) {
        uni.showToast({ title: '文件路径无效', icon: 'none' })
        return
      }

      uni.showLoading({ title: '上传中...' })

      // 使用正确的上传端点
      const uploadUrl = this.baseUrl + '/api/system/file/';
      console.log('上传URL:', uploadUrl);
      console.log('Token:', getToken());

      // 将头像上传到服务器
      uni.uploadFile({
        url: uploadUrl,
        filePath: filePath,
        name: 'file',
        header: {
          Authorization: 'JWT ' + getToken()  // 修复：使用JWT前缀
        },
        success: (uploadRes) => {
          console.log('上传原始响应:', uploadRes);
          try {
            const data = JSON.parse(uploadRes.data)
            console.log('上传解析后响应:', data);

            if (data.code === 2000) {  // 修复：检查2000状态码
              // 更新头像URL - 尝试多个可能的字段
              const avatarUrl = data.data?.url || data.url || data.data?.file_url
              console.log('获取到的头像URL:', avatarUrl);

              if (avatarUrl) {
                this.userInfo.avatar = avatarUrl

                // 同时更新用户信息到后端
                this.updateUserAvatar(avatarUrl)

                uni.hideLoading()
                uni.showToast({ title: '上传成功', icon: 'success' })
              } else {
                console.error('响应中没有找到头像URL');
                uni.hideLoading()
                uni.showToast({ title: '上传成功但获取URL失败', icon: 'none' })
              }
            } else {
              console.error('上传失败，错误码:', data.code, '错误信息:', data.msg);
              uni.hideLoading()
              uni.showToast({ title: data.msg || '上传失败', icon: 'none' })
            }
          } catch (e) {
            console.error('解析上传响应失败:', e, '原始响应:', uploadRes.data);
            uni.hideLoading()
            uni.showToast({ title: '上传失败', icon: 'none' })
          }
        },
        fail: (err) => {
          console.error('上传请求失败:', err);
          uni.hideLoading()
          uni.showToast({ title: '上传失败，请重试', icon: 'none' })
        }
      })
    },

    // 更新用户头像到后端
    updateUserAvatar(avatarUrl) {
      console.log('更新用户头像到后端:', avatarUrl);

      updateUserInfo({
        avatar_url: avatarUrl
      }).then(res => {
        if (res.code === 2000) {
          // 更新本地存储 - 统一字段名
          const userInfo = uni.getStorageSync('userInfo') || {}
          userInfo.avatarUrl = avatarUrl
          userInfo.avatar = avatarUrl
          userInfo.avatar_url = avatarUrl
          uni.setStorageSync('userInfo', userInfo)

          console.log('头像更新成功，本地存储已更新');
        } else {
          console.error('更新用户头像失败:', res.msg);
        }
      }).catch(err => {
        console.error('更新用户头像接口调用失败:', err);
      })
    },
    
    // 保存个人资料
    saveProfile() {
      if (!this.userInfo.nickname) {
        return uni.showToast({ title: '请输入昵称', icon: 'none' })
      }
      
      uni.showLoading({ title: '保存中...' })
      
      const updateData = {
        nickname: this.userInfo.nickname,
        avatar: this.userInfo.avatar
      }
      
      updateUserInfo(updateData).then(res => {
        uni.hideLoading()
        if (res.code === 200) {
          // 更新本地存储
          uni.setStorageSync('userInfo', {
            nickName: this.userInfo.nickname,
            avatarUrl: this.userInfo.avatar,
            gender: this.userInfo.gender || 0
          })
          
          uni.showToast({ title: '保存成功' })
          
          // 如果是首次登录，跳转到首页，否则返回上一页
          if (this.isFirstLogin) {
            this.$tab.reLaunch('/pages/index')
          } else {
            // 延迟返回
            setTimeout(() => {
              uni.navigateBack()
            }, 1500)
          }
        } else {
          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })
        }
      }).catch(() => {
        uni.hideLoading()
        uni.showToast({ title: '保存失败', icon: 'none' })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 30rpx;
  background-color: #ffffff;
  min-height: 100vh;
  
  .avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 50rpx;
    padding-top: 50rpx;
    
    .avatar-button {
      padding: 0;
      background: none;
      border: none;
      width: 180rpx;
      height: 180rpx;
      border-radius: 50%;
      overflow: hidden;
      position: relative;
      transition: transform 0.2s;

      &::after {
        border: none;
      }

      &:active {
        transform: scale(0.95);
      }

      .avatar {
        width: 180rpx;
        height: 180rpx;
        border-radius: 50%;
        border: 4rpx solid #f0f0f0;
        transition: border-color 0.2s;
      }

      &:hover .avatar {
        border-color: #4CAF50;
      }

      .avatar-overlay-button {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;

        &::after {
          border: none;
        }
      }
    }
    
    .tip {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #999;
    }

    .hidden-avatar-button {
      position: fixed;
      top: -200rpx;
      left: -200rpx;
      width: 1rpx;
      height: 1rpx;
      opacity: 0;
      pointer-events: none;
    }


  }
  
  .form-section {
    .form-item {
      margin-bottom: 30rpx;
      
      .label {
        display: block;
        font-size: 30rpx;
        color: #333;
        margin-bottom: 20rpx;
      }
      
      input {
        width: 100%;
        height: 90rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 30rpx;
      }
    }
    
    .save-btn {
      margin-top: 50rpx;
      height: 90rpx;
      line-height: 90rpx;
      font-size: 32rpx;
    }
  }
}
</style> 