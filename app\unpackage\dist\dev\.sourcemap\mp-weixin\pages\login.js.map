{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?f956", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?6681", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?5283", "uni-app:///pages/login.vue", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?5353", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?db5b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "globalConfig", "appInfo", "logo", "name", "loading", "mounted", "console", "onLoad", "uni", "url", "methods", "handlePrivacy", "handleUserAgrement", "wxQuickLogin", "title", "provider", "success", "nick<PERSON><PERSON>", "avatarUrl", "gender", "token", "userData", "icon", "nickname", "avatar", "avatar_url", "fail", "checkUserProfile", "user", "content", "confirmText", "cancelText", "handleWxLogin", "loginSuccess", "checkAndTriggerAvatarSelection"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqL;AACrL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4rB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACyChtB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QACAC;UACAC;UACAC;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;QACA;MACA;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAH;QACAC;MACA;IACA;IACA;IACAG;MACA;MACAJ;QACAC;MACA;IACA;IAEA;IACAI;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;QACAL;UACAM;QACA;MACA;;MAEA;MACAN;QACAO;QACAC;UACAV;UACA;YACA;YACA;cACAW;cACAC;cAAA;cACAC;YACA;;YAEA;YACA;cACA;cACA;cACA;gBACA;cACA;gBACAX;cACA;;cAEA;cACAF;cAEA;gBACA;gBACA;gBACA;;gBAEA;gBACA;kBACAc;gBACA;kBACAA;gBACA;;gBAEA;gBACA;kBACAC;gBACA;kBACAA;gBACA;kBACA;kBACA;oBACAA;kBACA;gBACA;;gBAEA;gBACA;kBACAf;kBACA;oBACA;kBACA;oBACAE;sBACAM;sBACAQ;oBACA;kBACA;kBACA;gBACA;;gBAEA;gBACA;gBACA;;gBAEA;gBACA;kBACAhB;kBACAe;oBACAE;oBACAC;oBACAL;kBACA;gBACA;;gBAEA;gBACA;gBACA;gBAEAX;kBACAS;kBACAM;kBACAL;kBACAM;kBACAC;kBACAN;gBACA;gBAEAb;kBACAiB;kBACAC;gBACA;;gBAEA;gBACA;cACA;gBACA;kBACA;gBACA;kBACAhB;oBACAM;oBACAQ;kBACA;gBACA;gBACAhB;cACA;YACA;cACA;cACA;gBACA;cACA;gBACAE;cACA;cACA;gBACA;cACA;gBACAA;kBACAM;kBACAQ;gBACA;cACA;cACAhB;YACA;UACA;YACA;YACA;cACA;YACA;cACAE;YACA;YACA;cACA;YACA;cACAA;gBACAM;gBACAQ;cACA;YACA;YACAhB;UACA;QACA;QACAoB;UACA;UACA;YACA;UACA;YACAlB;UACA;UACA;YACA;UACA;YACAA;cACAM;cACAQ;YACA;UACA;UACAhB;QACA;MACA;IACA;IAEA;IACAqB;MAAA;MACA;MACA;QACAC;QACAtB;MACA;;MAEA;MACA;MAEA;QACAE;UACAM;UACAe;UACAC;UACAC;UACAf;YACA;cACA;cACAR;gBACAC;cACA;YACA;cACA;cACA;YACA;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAuB;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA3B;QAEA;UACA;UACA;UAEA;YACAe;UACA;YACAA;UACA;UAEA;YACA;YACA;;YAEA;YACA;UACA;YACAf;YACA;YACAE;cACAC;YACA;UACA;QACA;UACA;YACA;UACA;YACAD;cACAM;cACAQ;YACA;UACA;UACA;UACA;UACA;QACA;MACA;QACAhB;QACA;UACA;QACA;UACAE;YACAM;YACAQ;UACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAY;MACA5B;;MAEA;MACA,uCACAe,qCACA,4CACA;MAEA;QACAf;QACA;QACAE;UACAC;QACA;QACA;MACA;MAEAH;;MAEA;MACAE;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3YA;AAAA;AAAA;AAAA;AAAu0C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACA31C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=18804380&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18804380\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=18804380&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"wx-login-container\">\n    <view class=\"logo-content align-center justify-center flex flex-direction\">\n      <image style=\"width: 120rpx;height: 120rpx;\" :src=\"globalConfig.appInfo.logo\" mode=\"widthFix\">\n      </image>\n      <text class=\"title\">电商平台</text>\n    </view>\n\n    <view class=\"login-form-content\">\n      <view class=\"welcome-text\">\n        <text class=\"welcome\">欢迎使用</text>\n        <text class=\"desc\">使用微信快捷登录体验更多功能</text>\n      </view>\n\n      <view class=\"action-btn\">\n        <!-- 微信小程序环境 -->\n        <!-- #ifdef MP-WEIXIN -->\n        <button @click=\"wxQuickLogin\" class=\"login-btn cu-btn block bg-green lg round\">\n          <text class=\"iconfont icon-wechat\" style=\"margin-right: 10rpx;\"></text>微信一键登录\n        </button>\n        <!-- #endif -->\n\n        <!-- 其他环境 -->\n        <!-- #ifndef MP-WEIXIN -->\n        <button @click=\"handleWxLogin\" class=\"login-btn cu-btn block bg-green lg round\">\n          <text class=\"iconfont icon-wechat\" style=\"margin-right: 10rpx;\"></text>微信一键登录\n        </button>\n        <!-- #endif -->\n      </view>\n\n      <view class=\"xieyi text-center\">\n        <text class=\"text-grey1\">登录即代表同意</text>\n        <text @click=\"handleUserAgrement\" class=\"text-blue\">《用户协议》</text>\n        <text class=\"text-grey1\">和</text>\n        <text @click=\"handlePrivacy\" class=\"text-blue\">《隐私政策》</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import { wxLogin, getInfo } from '@/api/auth'\n  import { getToken, setToken, removeToken } from '@/utils/auth'\n\n  export default {\n    data() {\n      return {\n        globalConfig: {\n          appInfo: {\n            logo: \"/static/images/logo.png\",\n            name: \"电商平台\"\n          }\n        },\n        loading: false\n      }\n    },\n    mounted() {\n      // 安全获取全局配置\n      try {\n        const app = getApp()\n        if (app && app.globalData && app.globalData.config) {\n          this.globalConfig = app.globalData.config\n        }\n      } catch (error) {\n        console.warn('获取全局配置失败，使用默认配置:', error)\n      }\n    },\n    onLoad() {\n      // 检查是否已登录\n      if (getToken()) {\n        // 使用 uni.reLaunch 替代 $tab.reLaunch\n        uni.reLaunch({\n          url: '/pages/index'\n        })\n      }\n    },\n    methods: {\n      // 隐私政策\n      handlePrivacy() {\n        // 直接使用type参数跳转到webview页面\n        uni.navigateTo({\n          url: '/pages/common/webview/index?type=privacy&title=' + encodeURIComponent('隐私政策')\n        })\n      },\n      // 用户协议\n      handleUserAgrement() {\n        // 直接使用type参数跳转到webview页面\n        uni.navigateTo({\n          url: '/pages/common/webview/index?type=agreement&title=' + encodeURIComponent('用户协议')\n        })\n      },\n\n      // 微信快速登录（直接使用code登录，不再获取用户信息）\n      wxQuickLogin() {\n        if (this.loading) return\n        this.loading = true\n        // 安全调用 $modal\n        if (this.$modal && this.$modal.loading) {\n          this.$modal.loading(\"登录中...\")\n        } else {\n          uni.showLoading({\n            title: '登录中...'\n          })\n        }\n\n        // 获取登录凭证code\n        uni.login({\n          provider: 'weixin',\n          success: (loginRes) => {\n            console.log('获取登录凭证成功:', loginRes)\n            if (loginRes.code) {\n              // 构建默认用户信息\n              const userInfo = {\n                nickName: '微信用户',\n                avatarUrl: '', // 默认头像，后端可以设置\n                gender: 0      // 性别未知\n              }\n\n              // 调用后端登录接口\n              wxLogin(loginRes.code, userInfo).then(res => {\n                this.loading = false\n                // 安全关闭加载框\n                if (this.$modal && this.$modal.closeLoading) {\n                  this.$modal.closeLoading()\n                } else {\n                  uni.hideLoading()\n                }\n\n                // 打印完整的返回数据，用于调试\n                console.log('登录接口返回数据:', JSON.stringify(res))\n\n                if (res.code === 2000) {\n                  // 登录成功，尝试提取token和用户信息\n                  let token = null;\n                  let userData = null;\n\n                  // 尝试从不同位置获取token\n                  if (res.data && res.data.token) {\n                    token = res.data.token;\n                  } else if (res.token) {\n                    token = res.token;\n                  }\n\n                  // 尝试从不同位置获取用户信息\n                  if (res.data && res.data.user) {\n                    userData = res.data.user;\n                  } else if (res.user) {\n                    userData = res.user;\n                  } else if (res.data) {\n                    // 如果data中没有user字段，可能整个data就是用户信息\n                    if (!res.data.token && !res.data.userId) {\n                      userData = res.data;\n                    }\n                  }\n\n                  // 如果没有找到token，报错并返回\n                  if (!token) {\n                    console.error('登录返回数据中未找到token:', res)\n                    if (this.$modal && this.$modal.msgError) {\n                      this.$modal.msgError(\"登录失败，未获取到授权信息\")\n                    } else {\n                      uni.showToast({\n                        title: '登录失败，未获取到授权信息',\n                        icon: 'none'\n                      })\n                    }\n                    return\n                  }\n\n                  // 保存token\n                  setToken(token)\n                  this.$store.commit('SET_TOKEN', token)\n\n                  // 如果没有找到用户数据，使用默认值\n                  if (!userData) {\n                    console.warn('登录返回数据中未找到用户信息，将使用默认值')\n                    userData = {\n                      nickname: userInfo.nickName,\n                      avatar: userInfo.avatarUrl,\n                      gender: userInfo.gender\n                    }\n                  }\n\n                  // 保存用户基本信息到本地 - 统一字段名\n                  const avatarUrl = userData.avatar_url || userData.avatar || userInfo.avatarUrl || ''\n                  const nickname = userData.nickname || userInfo.nickName || ''\n\n                  uni.setStorageSync('userInfo', {\n                    nickName: nickname,\n                    nickname: nickname,\n                    avatarUrl: avatarUrl,\n                    avatar: avatarUrl,\n                    avatar_url: avatarUrl,\n                    gender: userData.gender || userInfo.gender\n                  })\n\n                  console.log('登录成功，保存用户信息:', {\n                    nickname: nickname,\n                    avatar: avatarUrl\n                  })\n\n                  // 检查是否需要完善个人资料\n                  this.checkUserProfile(userData)\n                } else {\n                  if (this.$modal && this.$modal.msgError) {\n                    this.$modal.msgError(res.msg || \"登录失败，请重试\")\n                  } else {\n                    uni.showToast({\n                      title: res.msg || \"登录失败，请重试\",\n                      icon: 'none'\n                    })\n                  }\n                  console.error('登录失败', res)\n                }\n              }).catch((error) => {\n                this.loading = false\n                if (this.$modal && this.$modal.closeLoading) {\n                  this.$modal.closeLoading()\n                } else {\n                  uni.hideLoading()\n                }\n                if (this.$modal && this.$modal.msgError) {\n                  this.$modal.msgError(\"登录失败，请重试\")\n                } else {\n                  uni.showToast({\n                    title: \"登录失败，请重试\",\n                    icon: 'none'\n                  })\n                }\n                console.error('登录接口调用失败', error)\n              })\n            } else {\n              this.loading = false\n              if (this.$modal && this.$modal.closeLoading) {\n                this.$modal.closeLoading()\n              } else {\n                uni.hideLoading()\n              }\n              if (this.$modal && this.$modal.msgError) {\n                this.$modal.msgError(\"微信登录失败，请重试\")\n              } else {\n                uni.showToast({\n                  title: \"微信登录失败，请重试\",\n                  icon: 'none'\n                })\n              }\n              console.error('微信登录失败', loginRes)\n            }\n          },\n          fail: (err) => {\n            this.loading = false\n            if (this.$modal && this.$modal.closeLoading) {\n              this.$modal.closeLoading()\n            } else {\n              uni.hideLoading()\n            }\n            if (this.$modal && this.$modal.msgError) {\n              this.$modal.msgError(\"微信登录失败，请重试\")\n            } else {\n              uni.showToast({\n                title: \"微信登录失败，请重试\",\n                icon: 'none'\n              })\n            }\n            console.error('微信登录失败', err)\n          }\n        })\n      },\n\n      // 检查用户资料是否需要完善\n      checkUserProfile(user) {\n        // 防止user为undefined或null\n        if (!user) {\n          user = {}\n          console.warn('用户信息为空，将使用默认值')\n        }\n\n        // 如果用户昵称为\"微信用户\"或头像为空，则认为需要完善资料\n        const needComplete = !user.nickname || user.nickname === '微信用户' || (!user.avatar_url && !user.avatar)\n\n        if (needComplete) {\n          uni.showModal({\n            title: '完善个人资料',\n            content: '为了提供更好的服务，请完善您的个人资料',\n            confirmText: '去完善',\n            cancelText: '暂不完善',\n            success: (res) => {\n              if (res.confirm) {\n                // 跳转到个人资料页面，并标记为首次登录\n                uni.navigateTo({\n                  url: '/pages/mine/profile/index?first=1'\n                })\n              } else {\n                // 直接进入首页\n                this.loginSuccess()\n              }\n            }\n          })\n        } else {\n          // 资料已完善，直接进入首页\n          this.loginSuccess()\n        }\n      },\n\n      // 其他平台的微信登录方法\n      handleWxLogin() {\n        // 使用相同的快速登录逻辑\n        this.wxQuickLogin()\n      },\n\n      // 登录成功后，处理函数\n      loginSuccess() {\n        // 获取用户信息\n        getInfo().then(res => {\n          console.log('获取用户信息返回数据:', JSON.stringify(res))\n\n          if (res.code === 2000) {\n            // 尝试提取用户信息\n            let userData = null;\n\n            if (res.data) {\n              userData = res.data;\n            } else if (res.user) {\n              userData = res.user;\n            }\n\n            if (userData) {\n              // 更新用户信息到store\n              this.$store.commit('SET_USER_INFO', userData)\n\n              // 检查是否需要直接进入微信头像选择\n              this.checkAndTriggerAvatarSelection(userData)\n            } else {\n              console.warn('获取用户信息成功，但未找到用户数据')\n              // 跳转到首页\n              uni.reLaunch({\n                url: '/pages/index'\n              })\n            }\n          } else {\n            if (this.$modal && this.$modal.msgError) {\n              this.$modal.msgError(res.msg || \"获取用户信息失败，请重新登录\")\n            } else {\n              uni.showToast({\n                title: res.msg || \"获取用户信息失败，请重新登录\",\n                icon: 'none'\n              })\n            }\n            // 清除token\n            this.$store.commit('SET_TOKEN', '')\n            removeToken()\n          }\n        }).catch(error => {\n          console.error('获取用户信息失败:', error)\n          if (this.$modal && this.$modal.msgError) {\n            this.$modal.msgError(\"获取用户信息失败，请重新登录\")\n          } else {\n            uni.showToast({\n              title: \"获取用户信息失败，请重新登录\",\n              icon: 'none'\n            })\n          }\n          // 清除token\n          this.$store.commit('SET_TOKEN', '')\n          removeToken()\n        })\n      },\n\n      // 检查并触发头像选择\n      checkAndTriggerAvatarSelection(userData) {\n        console.log('检查是否需要进入个人资料页面:', userData);\n\n        // 检查用户是否已有头像\n        const hasAvatar = userData.avatar_url &&\n                         userData.avatar_url.trim() !== '' &&\n                         !userData.avatar_url.includes('default') &&\n                         !userData.avatar_url.includes('placeholder');\n\n        if (hasAvatar) {\n          console.log('用户已有头像，直接跳转首页:', userData.avatar_url);\n          // 跳转到首页\n          uni.reLaunch({\n            url: '/pages/index'\n          })\n          return;\n        }\n\n        console.log('用户无头像，跳转到个人资料页面进行头像选择');\n\n        // 跳转到个人资料页面，让用户选择头像\n        uni.reLaunch({\n          url: '/pages/mine/profile/index?first=1'\n        })\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  page {\n    background-color: #ffffff;\n  }\n\n  /* 添加缺失的工具类 */\n  .flex {\n    display: flex;\n  }\n\n  .flex-direction {\n    flex-direction: column;\n  }\n\n  .align-center {\n    align-items: center;\n  }\n\n  .justify-center {\n    justify-content: center;\n  }\n\n  .text-center {\n    text-align: center;\n  }\n\n  .text-grey1 {\n    color: #999;\n  }\n\n  .text-blue {\n    color: #007aff;\n  }\n\n  .block {\n    display: block;\n    width: 100%;\n  }\n\n  .round {\n    border-radius: 50rpx;\n  }\n\n  .lg {\n    padding: 0 60rpx;\n    height: 88rpx;\n    line-height: 88rpx;\n  }\n\n  .bg-green {\n    background-color: #09bb07;\n    color: #fff;\n  }\n\n  .cu-btn {\n    border: none;\n    font-size: 32rpx;\n    font-weight: 600;\n  }\n\n  .wx-login-container {\n    width: 100%;\n    min-height: 100vh;\n    background-color: #ffffff;\n\n    .logo-content {\n      width: 100%;\n      font-size: 24px;\n      text-align: center;\n      padding-top: 15%;\n\n      image {\n        border-radius: 8px;\n      }\n\n      .title {\n        margin-top: 15px;\n        font-weight: bold;\n      }\n    }\n\n    .login-form-content {\n      text-align: center;\n      margin: 20px auto;\n      margin-top: 20%;\n      width: 80%;\n\n      .welcome-text {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        margin-bottom: 60px;\n\n        .welcome {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 10px;\n        }\n\n        .desc {\n          font-size: 14px;\n          color: #999;\n        }\n      }\n\n      .login-btn {\n        margin-top: 40px;\n        height: 45px;\n        font-size: 16px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .xieyi {\n        color: #333;\n        margin-top: 30px;\n        font-size: 12px;\n      }\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751686077783\n      var cssReload = require(\"D:/System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}