{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?3395", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?373d", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?68fa", "uni-app:///pages/mine/profile/index.vue", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?d4e5", "webpack:///D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?4cc3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "avatar", "nickname", "defaultAvatar", "is<PERSON>irstL<PERSON>in", "baseUrl", "showWechatAvatarButton", "showWechatAvatarOverlay", "onLoad", "console", "methods", "checkWechatAvatarSupport", "onChooseWechatAvatar", "uni", "title", "icon", "chooseAvatarImage", "content", "showCancel", "confirmText", "success", "itemList", "showAvailableOptions", "fail", "chooseRealWechatAvatar", "duration", "setTimeout", "canUseWechatAvatar", "e", "cancelText", "avatarUrl", "desc", "count", "sizeType", "sourceType", "url", "timeout", "message", "filePath", "name", "header", "Authorization", "cleanUrl", "avatar_url", "nick<PERSON><PERSON>", "gender"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACiC9uB;AACA;AACA;AAAA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACAC;IAEA,gDACAT;MACAE;MACAD;IAAA,EACA;IAEAQ;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MACA;;MAEA;MACAF;;MAEA;MACA;QACA;MACA;IAEA;IAEA;IACAG;MACAH;MACA;MAEA;QACA;QACA;MACA;QACAI;UAAAC;UAAAC;QAAA;MACA;IACA;IAEA;IACAC;MAAA;MACAP;;MAEA;MACA;MACA;;MAEA;MACA;QACAA;QACAI;UACAC;UACAG;UACAC;UACAC;UACAC;YACA;YACA;UACA;QACA;QACA;MACA;;MAEA;MACA;QACAC;MACA;;MAEA;MACAA;MAEA;IACA;IAEA;IACAC;MAAA;MACAT;QACAQ;QACAD;UACA;UACAX;UAEA;YACA;UACA;YACA;UACA;YACA;UACA;QACA;QACAc;UACAd;QACA;MACA;IACA;IAEA;IACAe;MAAA;MACAf;;MAEA;MACA;MACAA;;MAEA;MACA;;MAEA;MACAI;QACAC;QACAC;QACAU;MACA;;MAEA;MACAC;QACA;UACA;UACAjB;QACA;MACA;IACA;IAEA;IACAkB;MAEA;MACA;MACA;QACAlB;QACA;MACA;MACA;MACA;IAMA;EAAA,iGAKAmB;IACAnB;;IAEA;IACA;IAEA;IACAA;IAEA;MACA;MACA;MACAA;MAEA;QACA;QACAA;QACA;MACA;QACA;QACAA;QACAI;UAAAC;QAAA;QACA;QACA;MACA;IACA;MACAL;MACAI;QACAC;QACAC;MACA;IACA;EACA,gHAGA;IAAA;IACAF;MACAC;MACAG;MACAE;MACAU;MACAT;QACA;UACA;QACA;UACA;QACA;MACA;IACA;EACA,8FAGAU;IACArB;;IAEA;IACA;MACAA;MACA;IACA;MACAA;MACA;MACAI;MACAA;QACAC;QACAC;MACA;IACA;EACA,gGAGAa;IAAA;IACAnB;;IAEA;IACA;;IAEA;IACA;IACA;MACAA;;MAEA;MACA;MACA;IACA;;IAEA;IACAI;MACAC;MACAC;IACA;EACA,0GAKAe;IACArB;;IAEA;IACA;MACAI;MACAA;QACAC;QACAC;MACA;MACA;IACA;;IAEA;IACA;IAEA;MACA;MACAN;MACA;IACA;MACA;MACAA;MACA;IACA;EACA,8GAGA;IAAA;IACAA;;IAEA;IACAI;MACAkB;MACAX;QACAX;QACA;QAEA;UACA;UACA;QACA;UACA;QACA;MACA;MACAc;QACAd;QACA;MACA;IACA;EACA,oGAGA;IAAA;IACAA;;IAEA;IACA;IACA;MACAA;MACA;MACA;MACA;IACA;;IAEA;IACAI;MACAC;MACAG;MACAG;QACA;UACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA;IACA;EACA,0GAGA;IAAA;IACAX;;IAEA;IACAI;MACAkB;MACAX;QACAX;QACA;QAEA;UACA;UACA;QACA;UACA;QACA;MACA;MACAc;QACAd;QACA;MACA;IACA;EACA,0GAGA;IAAA;IACAA;;IAEA;IACA;IACAI;MACAC;MACAG;MACAG;QACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA,0FAKA;IAAA;IACAP;MACAC;MACAG;MACAG;QACA;UACA;QACA;MACA;IACA;EACA,0FAGA;IAAA;IACAP;MACAmB;MACAC;MACAC;MACAd;QACAX;QACA;QACA;QACA;MACA;MACAc;QACAd;QACAI;UAAAC;UAAAC;QAAA;MACA;IACA;EACA,4FAGA;IAAA;IACAF;MACAmB;MACAC;MACAC;MACAd;QACAX;QACA;QACA;QACA;MACA;MACAc;QACAd;QACAI;UAAAC;UAAAC;QAAA;MACA;IACA;EACA,oHAGAe;IACArB;;IAEA;IACA;IAEA;MACA;MACAA;MACA;IACA;MACA;MACA;IACA;EACA,kGAGAqB;IAAA;IACArB;;IAEA;IACA;IACAI;IACAA;MACAC;MACAG;MACAE;MACAU;MACAT;QACA;UACA;QACA;UACA;UACA;UACAP;YACAC;YACAC;UACA;QACA;MACA;IACA;EACA,kHAGAe;IAAA;IACArB;;IAEA;IACAI;MACAsB;MACAC;MAAA;MACAhB;QACAX;QACA;UACA;UACA;QACA;UACAA;UACA;QACA;MACA;MACAc;QACAd;QACA;MACA;IACA;EACA,gGAGA4B;IAAA;IACAxB;IACAA;MACAC;MACAG;MACAE;MACAU;MACAT;QACA;UACA;QACA;MACA;IACA;EACA,wFAGAQ;IACA;EACA,kFAGAU;IAAA;IACA7B;;IAEA;IACA;MACA;MACA;IACA;;IAEA;IACA;IACA;MACA;MACA;IACA;;IAEA;IACA;IACAA;IACAA;;IAEA;IACAI;MACAsB;MACAG;MACAC;MACAH;MAAA;MACAI;QACAC;MACA;MACArB;QACAX;QACAA;;QAEA;QACA;UACA;UACA;QACA;QAEA;UACA;UACAA;UAEA;YAAA;YACA;YACA;YACAA;YAEA;cACA;cACAqB;cACArB;cAEA;gBACA;;gBAEA;gBACA;gBAEAI;gBACAA;kBAAAC;kBAAAC;gBAAA;cACA;gBACAN;gBACA;cACA;YACA;cACA;YACA;UACA;YACAA;YACA;UACA;QACA;UACAA;UACA;QACA;MACA;MACAc;QACAd;QACA;MACA;IACA;EACA,sFAGA0B;IACA;;IAEA;IACA;;IAEA;IACA;IACA;MACA1B;MACA;MACAiC;IACA;;IAEA;IACA;MACA;MACA;QACAA;MACA;QACAA;MACA;IACA;IAEA;EACA,0FAGAP;IACA;;IAEA;IACA;MACA;QACA;MACA;;MAEA;MACA;MACA;QAAA,OACAA;MAAA,EACA;MAEA;QACA1B;MACA;MAEA;IACA;MACAA;MACA;IACA;EACA,4FAGA4B;IAAA;IACAxB;IACAA;MACAC;MACAG;MACAE;MACAU;MACAT;QACA;UACA;QACA;MACA;IACA;EACA,0FAGAU;IACArB;IAEA;MACAkC;IACA;MACA;QACA;QACA;QACA3C;QACAA;QACAA;QACAa;QAEAJ;MACA;QACAA;MACA;IACA;MACAA;IACA;EACA,kFAGA;IAAA;IACA;MACA;QAAAK;QAAAC;MAAA;IACA;IAEAF;MAAAC;IAAA;IAEA;MACAZ;MACAD;IACA;IAEA;MACAY;MACA;QACA;QACAA;UACA+B;UACAd;UACAe;QACA;QAEAhC;UAAAC;QAAA;;QAEA;QACA;UACA;QACA;UACA;UACAY;YACAb;UACA;QACA;MACA;QACAA;UAAAC;UAAAC;QAAA;MACA;IACA;MACAF;MACAA;QAAAC;QAAAC;MAAA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACzxBA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/profile/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/profile/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=b1b03f2e&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b1b03f2e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=b1b03f2e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"profile-container\">\r\n    <view class=\"avatar-section\">\r\n      <!-- 头像显示区域 -->\r\n      <view @tap=\"chooseAvatarImage\" class=\"avatar-button\">\r\n        <image :src=\"userInfo.avatar || defaultAvatar\" class=\"avatar\"></image>\r\n\r\n        <!-- 微信头像选择按钮覆盖层 -->\r\n        <button\r\n          v-if=\"showWechatAvatarOverlay\"\r\n          open-type=\"chooseAvatar\"\r\n          @chooseavatar=\"onChooseWechatAvatar\"\r\n          @error=\"onChooseAvatarError\"\r\n          class=\"avatar-overlay-button\">\r\n          选择微信头像\r\n        </button>\r\n      </view>\r\n\r\n      <text class=\"tip\">点击更换头像</text>\r\n    </view>\r\n    \r\n    <view class=\"form-section\">\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">昵称</text>\r\n        <input type=\"nickname\" @change=\"onInputNickname\" :value=\"userInfo.nickname\" placeholder=\"请输入您的昵称\" />\r\n      </view>\r\n      \r\n      <button @click=\"saveProfile\" class=\"save-btn cu-btn block bg-green lg\">保存</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { updateUserInfo } from '@/api/auth'\r\nimport { getToken } from '@/utils/auth'\r\nimport config from '@/config'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userInfo: {\r\n        avatar: '',\r\n        nickname: ''\r\n      },\r\n      defaultAvatar: '/static/images/default-avatar.png',\r\n      isFirstLogin: false,\r\n      baseUrl: config.baseUrl || 'http://localhost:8080',\r\n      showWechatAvatarButton: false, // 控制是否显示微信头像按钮\r\n      showWechatAvatarOverlay: false // 控制头像覆盖层显示\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 获取当前用户信息\r\n    const userInfo = uni.getStorageSync('userInfo') || {}\r\n    console.log('从存储获取的用户信息:', userInfo);\r\n\r\n    this.userInfo = {\r\n      ...userInfo,\r\n      nickname: userInfo.nickName || userInfo.nickname || '',\r\n      avatar: userInfo.avatarUrl || userInfo.avatar || userInfo.avatar_url || ''\r\n    }\r\n\r\n    console.log('处理后的用户信息:', this.userInfo);\r\n\r\n    // 检查是否首次登录\r\n    this.isFirstLogin = options.first === '1'\r\n\r\n    // 检查当前环境是否支持 chooseAvatar\r\n    this.checkWechatAvatarSupport()\r\n  },\r\n  methods: {\r\n    // 检查微信头像支持\r\n    checkWechatAvatarSupport() {\r\n      // 检查是否在真机环境且支持 chooseAvatar\r\n      // #ifdef MP-WEIXIN\r\n      const systemInfo = uni.getSystemInfoSync()\r\n      console.log('系统信息:', systemInfo);\r\n\r\n      // 在真机上或者较新版本的开发者工具中启用\r\n      if (systemInfo.platform !== 'devtools' || systemInfo.SDKVersion >= '2.21.2') {\r\n        this.showWechatAvatarButton = false // 暂时禁用，避免ENOENT错误\r\n      }\r\n      // #endif\r\n    },\r\n\r\n    // 微信头像选择回调\r\n    onChooseWechatAvatar(e) {\r\n      console.log('微信头像选择回调:', e.detail);\r\n      const { avatarUrl } = e.detail\r\n\r\n      if (avatarUrl) {\r\n        this.userInfo.avatar = avatarUrl\r\n        this.downloadAndUploadWechatAvatar(avatarUrl)\r\n      } else {\r\n        uni.showToast({ title: '获取微信头像失败', icon: 'none' })\r\n      }\r\n    },\r\n\r\n    // 选择头像 - 提供多种选项\r\n    chooseAvatarImage() {\r\n      console.log('开始选择头像');\r\n\r\n      // 根据环境提供不同选项\r\n      const itemList = []\r\n      const systemInfo = uni.getSystemInfoSync()\r\n\r\n      // 在开发者工具中显示特殊提示\r\n      if (systemInfo.platform === 'devtools') {\r\n        console.log('开发者工具环境 - 显示环境提示');\r\n        uni.showModal({\r\n          title: '开发者工具提示',\r\n          content: '微信头像功能在开发者工具中无法正常使用，请选择其他方式上传头像。建议在真机上测试微信头像功能。',\r\n          showCancel: false,\r\n          confirmText: '知道了',\r\n          success: () => {\r\n            // 显示可用选项\r\n            this.showAvailableOptions(['从相册选择', '拍照'])\r\n          }\r\n        })\r\n        return\r\n      }\r\n\r\n      // 如果支持微信头像，添加到第一位\r\n      if (this.canUseWechatAvatar()) {\r\n        itemList.push('用微信头像')\r\n      }\r\n\r\n      // 添加基础选项\r\n      itemList.push('从相册选择', '拍照')\r\n\r\n      this.showAvailableOptions(itemList)\r\n    },\r\n\r\n    // 显示可用的头像选项\r\n    showAvailableOptions(itemList) {\r\n      uni.showActionSheet({\r\n        itemList: itemList,\r\n        success: (res) => {\r\n          const selectedOption = itemList[res.tapIndex]\r\n          console.log('用户选择了:', selectedOption);\r\n\r\n          if (selectedOption.includes('用微信头像')) {\r\n            this.chooseRealWechatAvatar()\r\n          } else if (selectedOption === '从相册选择') {\r\n            this.chooseFromAlbum()\r\n          } else if (selectedOption === '拍照') {\r\n            this.chooseFromCamera()\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.log('用户取消选择');\r\n        }\r\n      })\r\n    },\r\n\r\n    // 选择真实的微信头像\r\n    chooseRealWechatAvatar() {\r\n      console.log('选择真实的微信头像');\r\n\r\n      // 检查环境和版本支持\r\n      const systemInfo = uni.getSystemInfoSync()\r\n      console.log('系统信息:', systemInfo);\r\n\r\n      // 直接显示微信头像选择按钮\r\n      this.showWechatAvatarOverlay = true\r\n\r\n      // 显示提示\r\n      uni.showToast({\r\n        title: '请点击头像选择微信头像',\r\n        icon: 'none',\r\n        duration: 2000\r\n      })\r\n\r\n      // 10秒后自动隐藏覆盖层（给用户足够时间）\r\n      setTimeout(() => {\r\n        if (this.showWechatAvatarOverlay) {\r\n          this.showWechatAvatarOverlay = false\r\n          console.log('覆盖层自动隐藏');\r\n        }\r\n      }, 10000)\r\n    },\r\n\r\n    // 检查是否可以使用微信头像\r\n    canUseWechatAvatar() {\r\n      // #ifdef MP-WEIXIN\r\n      // 检查运行环境，开发者工具中禁用微信头像功能\r\n      const systemInfo = uni.getSystemInfoSync()\r\n      if (systemInfo.platform === 'devtools') {\r\n        console.log('开发者工具环境 - 禁用微信头像功能');\r\n        return false\r\n      }\r\n      // 真机环境中启用微信头像功能\r\n      return true\r\n      // #endif\r\n\r\n      // #ifndef MP-WEIXIN\r\n      return false\r\n      // #endif\r\n    },\r\n\r\n\r\n\r\n    // 处理微信头像选择回调\r\n    onChooseWechatAvatar(e) {\r\n      console.log('微信头像选择回调:', e);\r\n\r\n      // 隐藏覆盖层\r\n      this.showWechatAvatarOverlay = false\r\n\r\n      const { avatarUrl } = e.detail\r\n      console.log('获取到的微信头像URL:', avatarUrl);\r\n\r\n      if (avatarUrl && avatarUrl.trim() !== '') {\r\n        // 检查运行环境\r\n        const systemInfo = uni.getSystemInfoSync()\r\n        console.log('当前运行环境:', systemInfo.platform);\r\n\r\n        if (systemInfo.platform === 'devtools') {\r\n          // 开发者工具环境：直接提示用户使用其他方式\r\n          console.log('开发者工具环境 - 微信头像功能受限');\r\n          this.handleDevtoolsWechatAvatar()\r\n        } else {\r\n          // 真机环境：正常处理\r\n          console.log('真机环境 - 处理微信头像');\r\n          uni.showLoading({ title: '设置头像中...' })\r\n          this.userInfo.avatar = avatarUrl\r\n          this.handleWechatAvatar(avatarUrl)\r\n        }\r\n      } else {\r\n        console.error('未获取到有效的头像URL');\r\n        uni.showToast({\r\n          title: '未获取到头像，请重试',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 开发者工具中处理微信头像\r\n    handleDevtoolsWechatAvatar() {\r\n      uni.showModal({\r\n        title: '开发者工具限制',\r\n        content: '微信开发者工具中无法正常使用微信头像功能，请选择其他方式上传头像，或在真机上测试微信头像功能。',\r\n        confirmText: '从相册选择',\r\n        cancelText: '拍照',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.chooseFromAlbum()\r\n          } else if (res.cancel) {\r\n            this.chooseFromCamera()\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理微信头像\r\n    handleWechatAvatar(avatarUrl) {\r\n      console.log('处理微信头像:', avatarUrl);\r\n\r\n      // 检查是否是临时文件路径\r\n      if (avatarUrl.includes('__tmp__') || avatarUrl.includes('tmp') || avatarUrl.includes('WeappFileSystem')) {\r\n        console.log('检测到临时文件，需要下载并上传');\r\n        this.downloadAndUploadWechatAvatar(avatarUrl)\r\n      } else {\r\n        console.log('直接使用微信头像URL');\r\n        this.updateUserAvatar(avatarUrl)\r\n        uni.hideLoading()\r\n        uni.showToast({\r\n          title: '头像设置成功',\r\n          icon: 'success'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 处理微信头像选择错误\r\n    onChooseAvatarError(e) {\r\n      console.log('微信头像选择错误 (可忽略的开发者工具问题):', e);\r\n\r\n      // 隐藏覆盖层\r\n      this.showWechatAvatarOverlay = false\r\n\r\n      // 检查是否是开发者工具的ENOENT错误\r\n      const errorMsg = e.detail?.errMsg || e.errMsg || ''\r\n      if (errorMsg.includes('ENOENT') || errorMsg.includes('no such file')) {\r\n        console.log('这是开发者工具的已知问题，不影响功能');\r\n\r\n        // 不显示错误提示，因为这是开发者工具的问题\r\n        // 在真机上不会出现此错误\r\n        return\r\n      }\r\n\r\n      // 其他错误才显示提示\r\n      uni.showToast({\r\n        title: '头像选择失败，请重试',\r\n        icon: 'none'\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 处理选择的微信头像\r\n    handleChosenWechatAvatar(avatarUrl) {\r\n      console.log('处理选择的微信头像:', avatarUrl);\r\n\r\n      // 检查URL是否有效\r\n      if (!avatarUrl || avatarUrl.trim() === '') {\r\n        uni.hideLoading()\r\n        uni.showToast({\r\n          title: '头像URL无效',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      // 检查环境决定处理方式\r\n      const systemInfo = uni.getSystemInfoSync()\r\n\r\n      if (systemInfo.platform === 'devtools') {\r\n        // 开发者工具：直接使用URL，不下载\r\n        console.log('开发者工具环境 - 直接使用头像URL');\r\n        this.uploadWechatAvatarUrl(avatarUrl)\r\n      } else {\r\n        // 真机：尝试下载后上传\r\n        console.log('真机环境 - 尝试下载后上传');\r\n        this.downloadWechatAvatarOnDevice(avatarUrl)\r\n      }\r\n    },\r\n\r\n    // 在开发者工具中获取微信头像\r\n    getWechatAvatarInDevtools() {\r\n      console.log('开发者工具环境 - 使用安全的头像获取方式');\r\n\r\n      // 方法1: 尝试 getUserProfile\r\n      uni.getUserProfile({\r\n        desc: '用于完善用户资料',\r\n        success: (res) => {\r\n          console.log('getUserProfile成功:', res);\r\n          const avatarUrl = res.userInfo.avatarUrl\r\n\r\n          if (avatarUrl && avatarUrl !== '') {\r\n            this.userInfo.avatar = avatarUrl\r\n            this.downloadAndUploadWechatAvatar(avatarUrl)\r\n          } else {\r\n            this.tryGetStoredUserInfo()\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.log('getUserProfile失败:', err);\r\n          this.tryGetStoredUserInfo()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 尝试获取已存储的用户信息\r\n    tryGetStoredUserInfo() {\r\n      console.log('尝试获取已存储的用户信息');\r\n\r\n      // 从本地存储获取用户信息\r\n      const storedUserInfo = uni.getStorageSync('userInfo')\r\n      if (storedUserInfo && storedUserInfo.avatarUrl) {\r\n        console.log('使用已存储的头像:', storedUserInfo.avatarUrl);\r\n        this.userInfo.avatar = storedUserInfo.avatarUrl\r\n        this.downloadAndUploadWechatAvatar(storedUserInfo.avatarUrl)\r\n        return\r\n      }\r\n\r\n      // 如果没有存储的头像，提供模拟数据用于开发测试\r\n      uni.showModal({\r\n        title: '开发者工具提示',\r\n        content: '在开发者工具中无法获取真实微信头像，是否使用测试头像？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 使用一个测试头像URL\r\n            const testAvatarUrl = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'\r\n            this.userInfo.avatar = testAvatarUrl\r\n            this.downloadAndUploadWechatAvatar(testAvatarUrl)\r\n          } else {\r\n            this.fallbackToAlbum()\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 在真机上获取微信头像\r\n    getWechatAvatarOnDevice() {\r\n      console.log('真机环境 - 使用完整的头像获取功能');\r\n\r\n      // 方法1: 优先使用 getUserProfile\r\n      uni.getUserProfile({\r\n        desc: '用于完善用户资料',\r\n        success: (res) => {\r\n          console.log('getUserProfile成功:', res);\r\n          const avatarUrl = res.userInfo.avatarUrl\r\n\r\n          if (avatarUrl && avatarUrl !== '') {\r\n            this.userInfo.avatar = avatarUrl\r\n            this.downloadAndUploadWechatAvatar(avatarUrl)\r\n          } else {\r\n            this.tryChooseAvatarOnDevice()\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.log('getUserProfile失败，尝试其他方法:', err);\r\n          this.tryChooseAvatarOnDevice()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 在真机上尝试使用 chooseAvatar\r\n    tryChooseAvatarOnDevice() {\r\n      console.log('真机环境 - 尝试使用chooseAvatar');\r\n\r\n      // 创建一个临时的 chooseAvatar 按钮\r\n      // 注意：这里我们不直接使用模板中的按钮，而是通过编程方式触发\r\n      uni.showModal({\r\n        title: '获取微信头像',\r\n        content: '请点击确定后选择微信头像',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 这里可以触发隐藏的 chooseAvatar 按钮\r\n            // 或者降级到其他方案\r\n            this.fallbackToAlbum()\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 降级到相册选择\r\n    fallbackToAlbum() {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '无法获取微信头像，是否从相册选择？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.chooseFromAlbum()\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 从相册选择\r\n    chooseFromAlbum() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['album'],\r\n        success: (res) => {\r\n          console.log('从相册选择成功:', res);\r\n          const tempFilePath = res.tempFilePaths[0]\r\n          this.userInfo.avatar = tempFilePath\r\n          this.uploadAvatar(tempFilePath)\r\n        },\r\n        fail: (err) => {\r\n          console.error('从相册选择失败:', err);\r\n          uni.showToast({ title: '选择图片失败', icon: 'none' })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 拍照\r\n    chooseFromCamera() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['camera'],\r\n        success: (res) => {\r\n          console.log('拍照成功:', res);\r\n          const tempFilePath = res.tempFilePaths[0]\r\n          this.userInfo.avatar = tempFilePath\r\n          this.uploadAvatar(tempFilePath)\r\n        },\r\n        fail: (err) => {\r\n          console.error('拍照失败:', err);\r\n          uni.showToast({ title: '拍照失败', icon: 'none' })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 下载并上传微信头像\r\n    downloadAndUploadWechatAvatar(avatarUrl) {\r\n      console.log('开始处理微信头像:', avatarUrl);\r\n\r\n      // 检查运行环境\r\n      const systemInfo = uni.getSystemInfoSync()\r\n\r\n      if (systemInfo.platform === 'devtools') {\r\n        // 开发者工具环境：提示用户或使用备用方案\r\n        console.log('开发者工具环境 - 使用备用方案');\r\n        this.handleDevtoolsAvatar(avatarUrl)\r\n      } else {\r\n        // 真机环境：正常下载流程\r\n        this.downloadWechatAvatarOnDevice(avatarUrl)\r\n      }\r\n    },\r\n\r\n    // 开发者工具中处理微信头像\r\n    handleDevtoolsAvatar(avatarUrl) {\r\n      console.log('开发者工具环境 - 处理微信头像');\r\n\r\n      // 在开发者工具中，临时文件可能无法正常访问\r\n      // 我们提供一个备用方案\r\n      uni.hideLoading()\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '开发者工具中微信头像可能无法正常显示，建议在真机上测试。是否使用其他方式选择头像？',\r\n        confirmText: '从相册选择',\r\n        cancelText: '继续使用',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.chooseFromAlbum()\r\n          } else {\r\n            // 继续使用微信头像URL，但可能显示异常\r\n            this.updateUserAvatar(avatarUrl)\r\n            uni.showToast({\r\n              title: '头像已设置（可能显示异常）',\r\n              icon: 'none'\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 真机环境下载微信头像\r\n    downloadWechatAvatarOnDevice(avatarUrl) {\r\n      console.log('真机环境 - 下载微信头像:', avatarUrl);\r\n\r\n      // 在真机上正常下载微信头像\r\n      uni.downloadFile({\r\n        url: avatarUrl,\r\n        timeout: 10000, // 设置超时时间\r\n        success: (res) => {\r\n          console.log('下载微信头像成功:', res);\r\n          if (res.statusCode === 200 && res.tempFilePath) {\r\n            // 上传下载的头像文件\r\n            this.uploadAvatar(res.tempFilePath)\r\n          } else {\r\n            console.error('下载失败，状态码:', res.statusCode);\r\n            this.handleDownloadError('下载头像失败')\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.error('下载微信头像失败:', err);\r\n          this.handleDownloadError('网络错误，下载失败')\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理下载错误\r\n    handleDownloadError(message) {\r\n      uni.hideLoading()\r\n      uni.showModal({\r\n        title: '下载失败',\r\n        content: `${message}，是否尝试其他方式选择头像？`,\r\n        confirmText: '从相册选择',\r\n        cancelText: '取消',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.chooseFromAlbum()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 输入昵称回调\r\n    onInputNickname(e) {\r\n      this.userInfo.nickname = e.detail.value\r\n    },\r\n    \r\n    // 上传头像\r\n    uploadAvatar(filePath) {\r\n      console.log('开始上传头像:', filePath);\r\n\r\n      // 检查文件路径是否有效\r\n      if (!filePath) {\r\n        this.handleUploadError('文件路径无效')\r\n        return\r\n      }\r\n\r\n      // 检查token是否存在\r\n      const token = getToken()\r\n      if (!token) {\r\n        this.handleUploadError('登录状态已过期，请重新登录')\r\n        return\r\n      }\r\n\r\n      // 使用正确的上传端点\r\n      const uploadUrl = this.baseUrl + '/api/system/file/';\r\n      console.log('上传URL:', uploadUrl);\r\n      console.log('Token存在:', !!token);\r\n\r\n      // 将头像上传到服务器\r\n      uni.uploadFile({\r\n        url: uploadUrl,\r\n        filePath: filePath,\r\n        name: 'file',\r\n        timeout: 30000, // 30秒超时\r\n        header: {\r\n          Authorization: 'JWT ' + token\r\n        },\r\n        success: (uploadRes) => {\r\n          console.log('上传响应状态码:', uploadRes.statusCode);\r\n          console.log('上传原始响应:', uploadRes);\r\n\r\n          // 检查HTTP状态码\r\n          if (uploadRes.statusCode !== 200) {\r\n            this.handleUploadError(`服务器错误 (${uploadRes.statusCode})`)\r\n            return\r\n          }\r\n\r\n          try {\r\n            const data = JSON.parse(uploadRes.data)\r\n            console.log('上传解析后响应:', data);\r\n\r\n            if (data.code === 2000) {\r\n              // 更新头像URL - 尝试多个可能的字段\r\n              let avatarUrl = data.data?.url || data.url || data.data?.file_url\r\n              console.log('原始头像URL:', avatarUrl);\r\n\r\n              if (avatarUrl) {\r\n                // 清理和验证URL\r\n                avatarUrl = this.cleanAvatarUrl(avatarUrl)\r\n                console.log('清理后的头像URL:', avatarUrl);\r\n\r\n                if (this.isValidAvatarUrl(avatarUrl)) {\r\n                  this.userInfo.avatar = avatarUrl\r\n\r\n                  // 同时更新用户信息到后端\r\n                  this.updateUserAvatar(avatarUrl)\r\n\r\n                  uni.hideLoading()\r\n                  uni.showToast({ title: '头像上传成功', icon: 'success' })\r\n                } else {\r\n                  console.error('头像URL格式无效:', avatarUrl);\r\n                  this.handleUploadError('头像URL格式无效，请重试')\r\n                }\r\n              } else {\r\n                this.handleUploadError('上传成功但获取URL失败')\r\n              }\r\n            } else {\r\n              console.error('上传失败，错误码:', data.code, '错误信息:', data.msg);\r\n              this.handleUploadError(data.msg || '上传失败')\r\n            }\r\n          } catch (e) {\r\n            console.error('解析上传响应失败:', e, '原始响应:', uploadRes.data);\r\n            this.handleUploadError('服务器响应格式错误')\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.error('上传请求失败:', err);\r\n          this.handleUploadError('网络错误，请检查网络连接')\r\n        }\r\n      })\r\n    },\r\n\r\n    // 清理头像URL\r\n    cleanAvatarUrl(url) {\r\n      if (!url) return url\r\n\r\n      // 移除可能的特殊字符和编码问题\r\n      let cleanUrl = url.toString().trim()\r\n\r\n      // 检查是否包含异常字符\r\n      const hasInvalidChars = /[\\x00-\\x1F\\x7F-\\x9F]/.test(cleanUrl)\r\n      if (hasInvalidChars) {\r\n        console.warn('URL包含无效字符，尝试清理:', cleanUrl);\r\n        // 移除控制字符\r\n        cleanUrl = cleanUrl.replace(/[\\x00-\\x1F\\x7F-\\x9F]/g, '')\r\n      }\r\n\r\n      // 确保URL格式正确\r\n      if (!cleanUrl.startsWith('http') && !cleanUrl.startsWith('/')) {\r\n        // 如果是相对路径，添加基础路径\r\n        if (cleanUrl.startsWith('media/')) {\r\n          cleanUrl = `${this.baseUrl}/${cleanUrl}`\r\n        } else {\r\n          cleanUrl = `${this.baseUrl}/media/${cleanUrl}`\r\n        }\r\n      }\r\n\r\n      return cleanUrl\r\n    },\r\n\r\n    // 验证头像URL是否有效\r\n    isValidAvatarUrl(url) {\r\n      if (!url) return false\r\n\r\n      // 检查URL格式\r\n      try {\r\n        if (url.startsWith('http')) {\r\n          new URL(url)\r\n        }\r\n\r\n        // 检查是否包含图片扩展名\r\n        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']\r\n        const hasImageExt = imageExtensions.some(ext =>\r\n          url.toLowerCase().includes(ext)\r\n        )\r\n\r\n        if (!hasImageExt) {\r\n          console.warn('URL不包含图片扩展名:', url);\r\n        }\r\n\r\n        return true\r\n      } catch (e) {\r\n        console.error('URL格式验证失败:', e, url);\r\n        return false\r\n      }\r\n    },\r\n\r\n    // 处理上传错误\r\n    handleUploadError(message) {\r\n      uni.hideLoading()\r\n      uni.showModal({\r\n        title: '上传失败',\r\n        content: `${message}，是否重试或选择其他方式？`,\r\n        confirmText: '重新选择',\r\n        cancelText: '取消',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.chooseAvatarImage()\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 更新用户头像到后端\r\n    updateUserAvatar(avatarUrl) {\r\n      console.log('更新用户头像到后端:', avatarUrl);\r\n\r\n      updateUserInfo({\r\n        avatar_url: avatarUrl\r\n      }).then(res => {\r\n        if (res.code === 2000) {\r\n          // 更新本地存储 - 统一字段名\r\n          const userInfo = uni.getStorageSync('userInfo') || {}\r\n          userInfo.avatarUrl = avatarUrl\r\n          userInfo.avatar = avatarUrl\r\n          userInfo.avatar_url = avatarUrl\r\n          uni.setStorageSync('userInfo', userInfo)\r\n\r\n          console.log('头像更新成功，本地存储已更新');\r\n        } else {\r\n          console.error('更新用户头像失败:', res.msg);\r\n        }\r\n      }).catch(err => {\r\n        console.error('更新用户头像接口调用失败:', err);\r\n      })\r\n    },\r\n    \r\n    // 保存个人资料\r\n    saveProfile() {\r\n      if (!this.userInfo.nickname) {\r\n        return uni.showToast({ title: '请输入昵称', icon: 'none' })\r\n      }\r\n      \r\n      uni.showLoading({ title: '保存中...' })\r\n      \r\n      const updateData = {\r\n        nickname: this.userInfo.nickname,\r\n        avatar: this.userInfo.avatar\r\n      }\r\n      \r\n      updateUserInfo(updateData).then(res => {\r\n        uni.hideLoading()\r\n        if (res.code === 200) {\r\n          // 更新本地存储\r\n          uni.setStorageSync('userInfo', {\r\n            nickName: this.userInfo.nickname,\r\n            avatarUrl: this.userInfo.avatar,\r\n            gender: this.userInfo.gender || 0\r\n          })\r\n          \r\n          uni.showToast({ title: '保存成功' })\r\n          \r\n          // 如果是首次登录，跳转到首页，否则返回上一页\r\n          if (this.isFirstLogin) {\r\n            this.$tab.reLaunch('/pages/index')\r\n          } else {\r\n            // 延迟返回\r\n            setTimeout(() => {\r\n              uni.navigateBack()\r\n            }, 1500)\r\n          }\r\n        } else {\r\n          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })\r\n        }\r\n      }).catch(() => {\r\n        uni.hideLoading()\r\n        uni.showToast({ title: '保存失败', icon: 'none' })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.profile-container {\r\n  padding: 30rpx;\r\n  background-color: #ffffff;\r\n  min-height: 100vh;\r\n  \r\n  .avatar-section {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-bottom: 50rpx;\r\n    padding-top: 50rpx;\r\n    \r\n    .avatar-button {\r\n      padding: 0;\r\n      background: none;\r\n      border: none;\r\n      width: 180rpx;\r\n      height: 180rpx;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n      position: relative;\r\n      transition: transform 0.2s;\r\n\r\n      &::after {\r\n        border: none;\r\n      }\r\n\r\n      &:active {\r\n        transform: scale(0.95);\r\n      }\r\n\r\n      .avatar {\r\n        width: 180rpx;\r\n        height: 180rpx;\r\n        border-radius: 50%;\r\n        border: 4rpx solid #f0f0f0;\r\n        transition: border-color 0.2s;\r\n      }\r\n\r\n      &:hover .avatar {\r\n        border-color: #4CAF50;\r\n      }\r\n\r\n      .avatar-overlay-button {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        background: rgba(0, 0, 0, 0.6);\r\n        color: white;\r\n        border: none;\r\n        border-radius: 50%;\r\n        font-size: 24rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        z-index: 10;\r\n\r\n        &::after {\r\n          border: none;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .tip {\r\n      margin-top: 20rpx;\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n\r\n    .hidden-avatar-button {\r\n      position: fixed;\r\n      top: -200rpx;\r\n      left: -200rpx;\r\n      width: 1rpx;\r\n      height: 1rpx;\r\n      opacity: 0;\r\n      pointer-events: none;\r\n    }\r\n\r\n\r\n  }\r\n  \r\n  .form-section {\r\n    .form-item {\r\n      margin-bottom: 30rpx;\r\n      \r\n      .label {\r\n        display: block;\r\n        font-size: 30rpx;\r\n        color: #333;\r\n        margin-bottom: 20rpx;\r\n      }\r\n      \r\n      input {\r\n        width: 100%;\r\n        height: 90rpx;\r\n        border: 1px solid #eee;\r\n        border-radius: 8rpx;\r\n        padding: 0 20rpx;\r\n        font-size: 30rpx;\r\n      }\r\n    }\r\n    \r\n    .save-btn {\r\n      margin-top: 50rpx;\r\n      height: 90rpx;\r\n      line-height: 90rpx;\r\n      font-size: 32rpx;\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751689495223\n      var cssReload = require(\"D:/System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}