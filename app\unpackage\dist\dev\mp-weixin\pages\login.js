(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/login"],{

/***/ 56:
/*!*********************************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/main.js?{"page":"pages%2Flogin"} ***!
  \*********************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _login = _interopRequireDefault(__webpack_require__(/*! ./pages/login.vue */ 57));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_login.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 57:
/*!****************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue ***!
  \****************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./login.vue?vue&type=template&id=18804380&scoped=true& */ 58);
/* harmony import */ var _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./login.vue?vue&type=script&lang=js& */ 60);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true& */ 62);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 37);

var renderjs





/* normalize component */

var component = Object(_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "18804380",
  null,
  false,
  _login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/login.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 58:
/*!***********************************************************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?vue&type=template&id=18804380&scoped=true& ***!
  \***********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=18804380&scoped=true& */ 59);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_18804380_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 59:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?vue&type=template&id=18804380&scoped=true& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 60:
/*!*****************************************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js& */ 61);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 61:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _auth = __webpack_require__(/*! @/api/auth */ 44);
var _auth2 = __webpack_require__(/*! @/utils/auth */ 31);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      globalConfig: {
        appInfo: {
          logo: "/static/images/logo.png",
          name: "电商平台"
        }
      },
      loading: false
    };
  },
  mounted: function mounted() {
    // 安全获取全局配置
    try {
      var app = getApp();
      if (app && app.globalData && app.globalData.config) {
        this.globalConfig = app.globalData.config;
      }
    } catch (error) {
      console.warn('获取全局配置失败，使用默认配置:', error);
    }
  },
  onLoad: function onLoad() {
    // 检查是否已登录
    if ((0, _auth2.getToken)()) {
      // 使用 uni.reLaunch 替代 $tab.reLaunch
      uni.reLaunch({
        url: '/pages/index'
      });
    }
  },
  methods: {
    // 隐私政策
    handlePrivacy: function handlePrivacy() {
      // 直接使用type参数跳转到webview页面
      uni.navigateTo({
        url: '/pages/common/webview/index?type=privacy&title=' + encodeURIComponent('隐私政策')
      });
    },
    // 用户协议
    handleUserAgrement: function handleUserAgrement() {
      // 直接使用type参数跳转到webview页面
      uni.navigateTo({
        url: '/pages/common/webview/index?type=agreement&title=' + encodeURIComponent('用户协议')
      });
    },
    // 微信快速登录（直接使用code登录，不再获取用户信息）
    wxQuickLogin: function wxQuickLogin() {
      var _this = this;
      if (this.loading) return;
      this.loading = true;
      // 安全调用 $modal
      if (this.$modal && this.$modal.loading) {
        this.$modal.loading("登录中...");
      } else {
        uni.showLoading({
          title: '登录中...'
        });
      }

      // 获取登录凭证code
      uni.login({
        provider: 'weixin',
        success: function success(loginRes) {
          console.log('获取登录凭证成功:', loginRes);
          if (loginRes.code) {
            // 构建默认用户信息
            var userInfo = {
              nickName: '微信用户',
              avatarUrl: '',
              // 默认头像，后端可以设置
              gender: 0 // 性别未知
            };

            // 调用后端登录接口
            (0, _auth.wxLogin)(loginRes.code, userInfo).then(function (res) {
              _this.loading = false;
              // 安全关闭加载框
              if (_this.$modal && _this.$modal.closeLoading) {
                _this.$modal.closeLoading();
              } else {
                uni.hideLoading();
              }

              // 打印完整的返回数据，用于调试
              console.log('登录接口返回数据:', JSON.stringify(res));
              if (res.code === 2000) {
                // 登录成功，尝试提取token和用户信息
                var token = null;
                var userData = null;

                // 尝试从不同位置获取token
                if (res.data && res.data.token) {
                  token = res.data.token;
                } else if (res.token) {
                  token = res.token;
                }

                // 尝试从不同位置获取用户信息
                if (res.data && res.data.user) {
                  userData = res.data.user;
                } else if (res.user) {
                  userData = res.user;
                } else if (res.data) {
                  // 如果data中没有user字段，可能整个data就是用户信息
                  if (!res.data.token && !res.data.userId) {
                    userData = res.data;
                  }
                }

                // 如果没有找到token，报错并返回
                if (!token) {
                  console.error('登录返回数据中未找到token:', res);
                  if (_this.$modal && _this.$modal.msgError) {
                    _this.$modal.msgError("登录失败，未获取到授权信息");
                  } else {
                    uni.showToast({
                      title: '登录失败，未获取到授权信息',
                      icon: 'none'
                    });
                  }
                  return;
                }

                // 保存token
                (0, _auth2.setToken)(token);
                _this.$store.commit('SET_TOKEN', token);

                // 如果没有找到用户数据，使用默认值
                if (!userData) {
                  console.warn('登录返回数据中未找到用户信息，将使用默认值');
                  userData = {
                    nickname: userInfo.nickName,
                    avatar: userInfo.avatarUrl,
                    gender: userInfo.gender
                  };
                }

                // 保存用户基本信息到本地
                uni.setStorageSync('userInfo', {
                  nickName: userData.nickname || userInfo.nickName,
                  avatarUrl: userData.avatar_url || userData.avatar || userInfo.avatarUrl,
                  gender: userData.gender || userInfo.gender
                });

                // 检查是否需要完善个人资料
                _this.checkUserProfile(userData);
              } else {
                if (_this.$modal && _this.$modal.msgError) {
                  _this.$modal.msgError(res.msg || "登录失败，请重试");
                } else {
                  uni.showToast({
                    title: res.msg || "登录失败，请重试",
                    icon: 'none'
                  });
                }
                console.error('登录失败', res);
              }
            }).catch(function (error) {
              _this.loading = false;
              if (_this.$modal && _this.$modal.closeLoading) {
                _this.$modal.closeLoading();
              } else {
                uni.hideLoading();
              }
              if (_this.$modal && _this.$modal.msgError) {
                _this.$modal.msgError("登录失败，请重试");
              } else {
                uni.showToast({
                  title: "登录失败，请重试",
                  icon: 'none'
                });
              }
              console.error('登录接口调用失败', error);
            });
          } else {
            _this.loading = false;
            if (_this.$modal && _this.$modal.closeLoading) {
              _this.$modal.closeLoading();
            } else {
              uni.hideLoading();
            }
            if (_this.$modal && _this.$modal.msgError) {
              _this.$modal.msgError("微信登录失败，请重试");
            } else {
              uni.showToast({
                title: "微信登录失败，请重试",
                icon: 'none'
              });
            }
            console.error('微信登录失败', loginRes);
          }
        },
        fail: function fail(err) {
          _this.loading = false;
          if (_this.$modal && _this.$modal.closeLoading) {
            _this.$modal.closeLoading();
          } else {
            uni.hideLoading();
          }
          if (_this.$modal && _this.$modal.msgError) {
            _this.$modal.msgError("微信登录失败，请重试");
          } else {
            uni.showToast({
              title: "微信登录失败，请重试",
              icon: 'none'
            });
          }
          console.error('微信登录失败', err);
        }
      });
    },
    // 检查用户资料是否需要完善
    checkUserProfile: function checkUserProfile(user) {
      var _this2 = this;
      // 防止user为undefined或null
      if (!user) {
        user = {};
        console.warn('用户信息为空，将使用默认值');
      }

      // 如果用户昵称为"微信用户"或头像为空，则认为需要完善资料
      var needComplete = !user.nickname || user.nickname === '微信用户' || !user.avatar_url && !user.avatar;
      if (needComplete) {
        uni.showModal({
          title: '完善个人资料',
          content: '为了提供更好的服务，请完善您的个人资料',
          confirmText: '去完善',
          cancelText: '暂不完善',
          success: function success(res) {
            if (res.confirm) {
              // 跳转到个人资料页面，并标记为首次登录
              uni.navigateTo({
                url: '/pages/mine/profile/index?first=1'
              });
            } else {
              // 直接进入首页
              _this2.loginSuccess();
            }
          }
        });
      } else {
        // 资料已完善，直接进入首页
        this.loginSuccess();
      }
    },
    // 其他平台的微信登录方法
    handleWxLogin: function handleWxLogin() {
      // 使用相同的快速登录逻辑
      this.wxQuickLogin();
    },
    // 登录成功后，处理函数
    loginSuccess: function loginSuccess() {
      var _this3 = this;
      // 获取用户信息
      (0, _auth.getInfo)().then(function (res) {
        console.log('获取用户信息返回数据:', JSON.stringify(res));
        if (res.code === 2000) {
          // 尝试提取用户信息
          var userData = null;
          if (res.data) {
            userData = res.data;
          } else if (res.user) {
            userData = res.user;
          }
          if (userData) {
            // 更新用户信息到store
            _this3.$store.commit('SET_USER_INFO', userData);

            // 检查是否需要直接进入微信头像选择
            _this3.checkAndTriggerAvatarSelection(userData);
          } else {
            console.warn('获取用户信息成功，但未找到用户数据');
            // 跳转到首页
            uni.reLaunch({
              url: '/pages/index'
            });
          }
        } else {
          if (_this3.$modal && _this3.$modal.msgError) {
            _this3.$modal.msgError(res.msg || "获取用户信息失败，请重新登录");
          } else {
            uni.showToast({
              title: res.msg || "获取用户信息失败，请重新登录",
              icon: 'none'
            });
          }
          // 清除token
          _this3.$store.commit('SET_TOKEN', '');
          (0, _auth2.removeToken)();
        }
      }).catch(function (error) {
        console.error('获取用户信息失败:', error);
        if (_this3.$modal && _this3.$modal.msgError) {
          _this3.$modal.msgError("获取用户信息失败，请重新登录");
        } else {
          uni.showToast({
            title: "获取用户信息失败，请重新登录",
            icon: 'none'
          });
        }
        // 清除token
        _this3.$store.commit('SET_TOKEN', '');
        (0, _auth2.removeToken)();
      });
    },
    // 检查并触发头像选择
    checkAndTriggerAvatarSelection: function checkAndTriggerAvatarSelection(userData) {
      var _this4 = this;
      console.log('检查是否需要直接进入微信头像选择:', userData);

      // 检查用户是否已有头像
      var hasAvatar = userData.avatar_url && userData.avatar_url.trim() !== '' && !userData.avatar_url.includes('default') && !userData.avatar_url.includes('placeholder');
      if (hasAvatar) {
        console.log('用户已有头像，直接跳转首页:', userData.avatar_url);
        // 跳转到首页
        uni.reLaunch({
          url: '/pages/index'
        });
        return;
      }
      console.log('用户无头像，直接进入微信头像选择界面');

      // 检查是否支持微信头像选择
      if (!uni.canIUse('button.open-type.chooseAvatar')) {
        console.log('当前环境不支持微信头像选择，跳转首页');
        uni.reLaunch({
          url: '/pages/index'
        });
        return;
      }

      // 延迟一点时间，然后直接触发头像选择
      setTimeout(function () {
        _this4.directTriggerWechatAvatar();
      }, 500);
    },
    // 直接触发微信头像选择
    directTriggerWechatAvatar: function directTriggerWechatAvatar() {
      var _this5 = this;
      console.log('直接触发微信头像选择');

      // 显示微信头像选择按钮
      this.showAvatarSelection = true;

      // 显示提示
      uni.showToast({
        title: '请选择您的微信头像',
        icon: 'none',
        duration: 2000
      });

      // 15秒后自动跳转首页（如果用户没有选择）
      setTimeout(function () {
        if (_this5.showAvatarSelection) {
          _this5.showAvatarSelection = false;
          console.log('用户未选择头像，自动跳转首页');
          uni.reLaunch({
            url: '/pages/index'
          });
        }
      }, 15000);
    },
    // 处理微信头像选择回调
    onChooseWechatAvatar: function onChooseWechatAvatar(e) {
      console.log('登录页面 - 微信头像选择回调:', e);
      this.showAvatarSelection = false;
      var avatarUrl = e.detail.avatarUrl;
      console.log('获取到的微信头像URL:', avatarUrl);
      if (avatarUrl && avatarUrl.trim() !== '') {
        // 显示加载提示
        uni.showLoading({
          title: '设置头像中...'
        });

        // 调用更新头像API
        this.updateUserAvatar(avatarUrl);
      } else {
        console.error('未获取到有效的头像URL，跳转首页');
        uni.reLaunch({
          url: '/pages/index'
        });
      }
    },
    // 更新用户头像
    updateUserAvatar: function updateUserAvatar(avatarUrl) {
      var _this6 = this;
      console.log('更新用户头像:', avatarUrl);
      updateUserInfo({
        avatar_url: avatarUrl
      }).then(function (res) {
        uni.hideLoading();
        if (res.code === 2000) {
          // 更新本地存储的用户信息
          var currentUserInfo = _this6.$store.state.user.userInfo || {};
          var updatedUserInfo = _objectSpread(_objectSpread({}, currentUserInfo), {}, {
            avatar_url: avatarUrl
          });
          _this6.$store.commit('SET_USER_INFO', updatedUserInfo);
          uni.showToast({
            title: '头像设置成功',
            icon: 'success',
            duration: 1500
          });
          console.log('头像更新成功，跳转首页');

          // 延迟跳转，让用户看到成功提示
          setTimeout(function () {
            uni.reLaunch({
              url: '/pages/index'
            });
          }, 1500);
        } else {
          console.error('头像更新失败:', res.msg);
          uni.showToast({
            title: res.msg || '头像设置失败',
            icon: 'none',
            duration: 2000
          });

          // 延迟跳转首页
          setTimeout(function () {
            uni.reLaunch({
              url: '/pages/index'
            });
          }, 2000);
        }
      }).catch(function (error) {
        uni.hideLoading();
        console.error('头像更新请求失败:', error);
        uni.showToast({
          title: '头像设置失败，请稍后重试',
          icon: 'none',
          duration: 2000
        });

        // 延迟跳转首页
        setTimeout(function () {
          uni.reLaunch({
            url: '/pages/index'
          });
        }, 2000);
      });
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 62:
/*!**************************************************************************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true& ***!
  \**************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true& */ 63);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_id_18804380_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 63:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/hanqc/project/pyProject/PlantHome/app/pages/login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[56,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/login.js.map