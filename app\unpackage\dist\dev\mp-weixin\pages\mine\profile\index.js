(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/mine/profile/index"],{

/***/ 106:
/*!**************************************************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/main.js?{"page":"pages%2Fmine%2Fprofile%2Findex"} ***!
  \**************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pages/mine/profile/index.vue */ 107));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 107:
/*!*****************************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue ***!
  \*****************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_b1b03f2e_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=b1b03f2e&scoped=true& */ 108);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 110);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_b1b03f2e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true& */ 112);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 37);

var renderjs





/* normalize component */

var component = Object(_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_b1b03f2e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_b1b03f2e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "b1b03f2e",
  null,
  false,
  _index_vue_vue_type_template_id_b1b03f2e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/mine/profile/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 108:
/*!************************************************************************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?vue&type=template&id=b1b03f2e&scoped=true& ***!
  \************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_b1b03f2e_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=b1b03f2e&scoped=true& */ 109);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_b1b03f2e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_b1b03f2e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_b1b03f2e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_b1b03f2e_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 109:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?vue&type=template&id=b1b03f2e&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 110:
/*!******************************************************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 111);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_System_java_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 111:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _auth = __webpack_require__(/*! @/api/auth */ 44);
var _auth2 = __webpack_require__(/*! @/utils/auth */ 31);
var _config = _interopRequireDefault(__webpack_require__(/*! @/config */ 30));
var _methods;
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      userInfo: {
        avatar: '',
        nickname: ''
      },
      defaultAvatar: '/static/images/default-avatar.png',
      isFirstLogin: false,
      baseUrl: _config.default.baseUrl || 'http://localhost:8080',
      showWechatAvatarButton: false,
      // 控制是否显示微信头像按钮
      showWechatAvatarOverlay: false // 控制头像覆盖层显示
    };
  },
  onLoad: function onLoad(options) {
    // 获取当前用户信息
    var userInfo = uni.getStorageSync('userInfo') || {};
    console.log('从存储获取的用户信息:', userInfo);
    this.userInfo = _objectSpread(_objectSpread({}, userInfo), {}, {
      nickname: userInfo.nickName || userInfo.nickname || '',
      avatar: userInfo.avatarUrl || userInfo.avatar || userInfo.avatar_url || ''
    });
    console.log('处理后的用户信息:', this.userInfo);

    // 检查是否首次登录
    this.isFirstLogin = options.first === '1';

    // 检查当前环境是否支持 chooseAvatar
    this.checkWechatAvatarSupport();
  },
  methods: (_methods = {
    // 检查微信头像支持
    checkWechatAvatarSupport: function checkWechatAvatarSupport() {
      // 检查是否在真机环境且支持 chooseAvatar

      var systemInfo = uni.getSystemInfoSync();
      console.log('系统信息:', systemInfo);

      // 在真机上或者较新版本的开发者工具中启用
      if (systemInfo.platform !== 'devtools' || systemInfo.SDKVersion >= '2.21.2') {
        this.showWechatAvatarButton = false; // 暂时禁用，避免ENOENT错误
      }
    },
    // 微信头像选择回调
    onChooseWechatAvatar: function onChooseWechatAvatar(e) {
      console.log('微信头像选择回调:', e.detail);
      var avatarUrl = e.detail.avatarUrl;
      if (avatarUrl) {
        this.userInfo.avatar = avatarUrl;
        this.downloadAndUploadWechatAvatar(avatarUrl);
      } else {
        uni.showToast({
          title: '获取微信头像失败',
          icon: 'none'
        });
      }
    },
    // 选择头像 - 提供多种选项
    chooseAvatarImage: function chooseAvatarImage() {
      var _this = this;
      console.log('开始选择头像');

      // 根据环境提供不同选项
      var itemList = [];
      var systemInfo = uni.getSystemInfoSync();

      // 在开发者工具中显示特殊提示
      if (systemInfo.platform === 'devtools') {
        console.log('开发者工具环境 - 显示环境提示');
        uni.showModal({
          title: '开发者工具提示',
          content: '微信头像功能在开发者工具中无法正常使用，请选择其他方式上传头像。建议在真机上测试微信头像功能。',
          showCancel: false,
          confirmText: '知道了',
          success: function success() {
            // 显示可用选项
            _this.showAvailableOptions(['从相册选择', '拍照']);
          }
        });
        return;
      }

      // 如果支持微信头像，添加到第一位
      if (this.canUseWechatAvatar()) {
        itemList.push('用微信头像');
      }

      // 添加基础选项
      itemList.push('从相册选择', '拍照');
      this.showAvailableOptions(itemList);
    },
    // 显示可用的头像选项
    showAvailableOptions: function showAvailableOptions(itemList) {
      var _this2 = this;
      uni.showActionSheet({
        itemList: itemList,
        success: function success(res) {
          var selectedOption = itemList[res.tapIndex];
          console.log('用户选择了:', selectedOption);
          if (selectedOption.includes('用微信头像')) {
            _this2.chooseRealWechatAvatar();
          } else if (selectedOption === '从相册选择') {
            _this2.chooseFromAlbum();
          } else if (selectedOption === '拍照') {
            _this2.chooseFromCamera();
          }
        },
        fail: function fail(err) {
          console.log('用户取消选择');
        }
      });
    },
    // 选择真实的微信头像
    chooseRealWechatAvatar: function chooseRealWechatAvatar() {
      var _this3 = this;
      console.log('选择真实的微信头像');

      // 检查环境和版本支持
      var systemInfo = uni.getSystemInfoSync();
      console.log('系统信息:', systemInfo);

      // 直接显示微信头像选择按钮
      this.showWechatAvatarOverlay = true;

      // 显示提示
      uni.showToast({
        title: '请点击头像选择微信头像',
        icon: 'none',
        duration: 2000
      });

      // 10秒后自动隐藏覆盖层（给用户足够时间）
      setTimeout(function () {
        if (_this3.showWechatAvatarOverlay) {
          _this3.showWechatAvatarOverlay = false;
          console.log('覆盖层自动隐藏');
        }
      }, 10000);
    },
    // 检查是否可以使用微信头像
    canUseWechatAvatar: function canUseWechatAvatar() {
      // 检查运行环境，开发者工具中禁用微信头像功能
      var systemInfo = uni.getSystemInfoSync();
      if (systemInfo.platform === 'devtools') {
        console.log('开发者工具环境 - 禁用微信头像功能');
        return false;
      }
      // 真机环境中启用微信头像功能
      return true;
    }
  }, (0, _defineProperty2.default)(_methods, "onChooseWechatAvatar", function onChooseWechatAvatar(e) {
    console.log('微信头像选择回调:', e);

    // 隐藏覆盖层
    this.showWechatAvatarOverlay = false;
    var avatarUrl = e.detail.avatarUrl;
    console.log('获取到的微信头像URL:', avatarUrl);
    if (avatarUrl && avatarUrl.trim() !== '') {
      // 检查运行环境
      var systemInfo = uni.getSystemInfoSync();
      console.log('当前运行环境:', systemInfo.platform);
      if (systemInfo.platform === 'devtools') {
        // 开发者工具环境：直接提示用户使用其他方式
        console.log('开发者工具环境 - 微信头像功能受限');
        this.handleDevtoolsWechatAvatar();
      } else {
        // 真机环境：正常处理
        console.log('真机环境 - 处理微信头像');
        uni.showLoading({
          title: '设置头像中...'
        });
        this.userInfo.avatar = avatarUrl;
        this.handleWechatAvatar(avatarUrl);
      }
    } else {
      console.error('未获取到有效的头像URL');
      uni.showToast({
        title: '未获取到头像，请重试',
        icon: 'none'
      });
    }
  }), (0, _defineProperty2.default)(_methods, "handleDevtoolsWechatAvatar", function handleDevtoolsWechatAvatar() {
    var _this4 = this;
    uni.showModal({
      title: '开发者工具限制',
      content: '微信开发者工具中无法正常使用微信头像功能，请选择其他方式上传头像，或在真机上测试微信头像功能。',
      confirmText: '从相册选择',
      cancelText: '拍照',
      success: function success(res) {
        if (res.confirm) {
          _this4.chooseFromAlbum();
        } else if (res.cancel) {
          _this4.chooseFromCamera();
        }
      }
    });
  }), (0, _defineProperty2.default)(_methods, "handleWechatAvatar", function handleWechatAvatar(avatarUrl) {
    console.log('处理微信头像:', avatarUrl);

    // 检查是否是临时文件路径
    if (avatarUrl.includes('__tmp__') || avatarUrl.includes('tmp') || avatarUrl.includes('WeappFileSystem')) {
      console.log('检测到临时文件，需要下载并上传');
      this.downloadAndUploadWechatAvatar(avatarUrl);
    } else {
      console.log('直接使用微信头像URL');
      this.updateUserAvatar(avatarUrl);
      uni.hideLoading();
      uni.showToast({
        title: '头像设置成功',
        icon: 'success'
      });
    }
  }), (0, _defineProperty2.default)(_methods, "onChooseAvatarError", function onChooseAvatarError(e) {
    var _e$detail;
    console.log('微信头像选择错误 (可忽略的开发者工具问题):', e);

    // 隐藏覆盖层
    this.showWechatAvatarOverlay = false;

    // 检查是否是开发者工具的ENOENT错误
    var errorMsg = ((_e$detail = e.detail) === null || _e$detail === void 0 ? void 0 : _e$detail.errMsg) || e.errMsg || '';
    if (errorMsg.includes('ENOENT') || errorMsg.includes('no such file')) {
      console.log('这是开发者工具的已知问题，不影响功能');

      // 不显示错误提示，因为这是开发者工具的问题
      // 在真机上不会出现此错误
      return;
    }

    // 其他错误才显示提示
    uni.showToast({
      title: '头像选择失败，请重试',
      icon: 'none'
    });
  }), (0, _defineProperty2.default)(_methods, "handleChosenWechatAvatar", function handleChosenWechatAvatar(avatarUrl) {
    console.log('处理选择的微信头像:', avatarUrl);

    // 检查URL是否有效
    if (!avatarUrl || avatarUrl.trim() === '') {
      uni.hideLoading();
      uni.showToast({
        title: '头像URL无效',
        icon: 'none'
      });
      return;
    }

    // 检查环境决定处理方式
    var systemInfo = uni.getSystemInfoSync();
    if (systemInfo.platform === 'devtools') {
      // 开发者工具：直接使用URL，不下载
      console.log('开发者工具环境 - 直接使用头像URL');
      this.uploadWechatAvatarUrl(avatarUrl);
    } else {
      // 真机：尝试下载后上传
      console.log('真机环境 - 尝试下载后上传');
      this.downloadWechatAvatarOnDevice(avatarUrl);
    }
  }), (0, _defineProperty2.default)(_methods, "getWechatAvatarInDevtools", function getWechatAvatarInDevtools() {
    var _this5 = this;
    console.log('开发者工具环境 - 使用安全的头像获取方式');

    // 方法1: 尝试 getUserProfile
    uni.getUserProfile({
      desc: '用于完善用户资料',
      success: function success(res) {
        console.log('getUserProfile成功:', res);
        var avatarUrl = res.userInfo.avatarUrl;
        if (avatarUrl && avatarUrl !== '') {
          _this5.userInfo.avatar = avatarUrl;
          _this5.downloadAndUploadWechatAvatar(avatarUrl);
        } else {
          _this5.tryGetStoredUserInfo();
        }
      },
      fail: function fail(err) {
        console.log('getUserProfile失败:', err);
        _this5.tryGetStoredUserInfo();
      }
    });
  }), (0, _defineProperty2.default)(_methods, "tryGetStoredUserInfo", function tryGetStoredUserInfo() {
    var _this6 = this;
    console.log('尝试获取已存储的用户信息');

    // 从本地存储获取用户信息
    var storedUserInfo = uni.getStorageSync('userInfo');
    if (storedUserInfo && storedUserInfo.avatarUrl) {
      console.log('使用已存储的头像:', storedUserInfo.avatarUrl);
      this.userInfo.avatar = storedUserInfo.avatarUrl;
      this.downloadAndUploadWechatAvatar(storedUserInfo.avatarUrl);
      return;
    }

    // 如果没有存储的头像，提供模拟数据用于开发测试
    uni.showModal({
      title: '开发者工具提示',
      content: '在开发者工具中无法获取真实微信头像，是否使用测试头像？',
      success: function success(res) {
        if (res.confirm) {
          // 使用一个测试头像URL
          var testAvatarUrl = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132';
          _this6.userInfo.avatar = testAvatarUrl;
          _this6.downloadAndUploadWechatAvatar(testAvatarUrl);
        } else {
          _this6.fallbackToAlbum();
        }
      }
    });
  }), (0, _defineProperty2.default)(_methods, "getWechatAvatarOnDevice", function getWechatAvatarOnDevice() {
    var _this7 = this;
    console.log('真机环境 - 使用完整的头像获取功能');

    // 方法1: 优先使用 getUserProfile
    uni.getUserProfile({
      desc: '用于完善用户资料',
      success: function success(res) {
        console.log('getUserProfile成功:', res);
        var avatarUrl = res.userInfo.avatarUrl;
        if (avatarUrl && avatarUrl !== '') {
          _this7.userInfo.avatar = avatarUrl;
          _this7.downloadAndUploadWechatAvatar(avatarUrl);
        } else {
          _this7.tryChooseAvatarOnDevice();
        }
      },
      fail: function fail(err) {
        console.log('getUserProfile失败，尝试其他方法:', err);
        _this7.tryChooseAvatarOnDevice();
      }
    });
  }), (0, _defineProperty2.default)(_methods, "tryChooseAvatarOnDevice", function tryChooseAvatarOnDevice() {
    var _this8 = this;
    console.log('真机环境 - 尝试使用chooseAvatar');

    // 创建一个临时的 chooseAvatar 按钮
    // 注意：这里我们不直接使用模板中的按钮，而是通过编程方式触发
    uni.showModal({
      title: '获取微信头像',
      content: '请点击确定后选择微信头像',
      success: function success(res) {
        if (res.confirm) {
          // 这里可以触发隐藏的 chooseAvatar 按钮
          // 或者降级到其他方案
          _this8.fallbackToAlbum();
        }
      }
    });
  }), (0, _defineProperty2.default)(_methods, "fallbackToAlbum", function fallbackToAlbum() {
    var _this9 = this;
    uni.showModal({
      title: '提示',
      content: '无法获取微信头像，是否从相册选择？',
      success: function success(res) {
        if (res.confirm) {
          _this9.chooseFromAlbum();
        }
      }
    });
  }), (0, _defineProperty2.default)(_methods, "chooseFromAlbum", function chooseFromAlbum() {
    var _this10 = this;
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: function success(res) {
        console.log('从相册选择成功:', res);
        var tempFilePath = res.tempFilePaths[0];
        _this10.userInfo.avatar = tempFilePath;
        _this10.uploadAvatar(tempFilePath);
      },
      fail: function fail(err) {
        console.error('从相册选择失败:', err);
        uni.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  }), (0, _defineProperty2.default)(_methods, "chooseFromCamera", function chooseFromCamera() {
    var _this11 = this;
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: function success(res) {
        console.log('拍照成功:', res);
        var tempFilePath = res.tempFilePaths[0];
        _this11.userInfo.avatar = tempFilePath;
        _this11.uploadAvatar(tempFilePath);
      },
      fail: function fail(err) {
        console.error('拍照失败:', err);
        uni.showToast({
          title: '拍照失败',
          icon: 'none'
        });
      }
    });
  }), (0, _defineProperty2.default)(_methods, "downloadAndUploadWechatAvatar", function downloadAndUploadWechatAvatar(avatarUrl) {
    console.log('开始处理微信头像:', avatarUrl);

    // 检查运行环境
    var systemInfo = uni.getSystemInfoSync();
    if (systemInfo.platform === 'devtools') {
      // 开发者工具环境：提示用户或使用备用方案
      console.log('开发者工具环境 - 使用备用方案');
      this.handleDevtoolsAvatar(avatarUrl);
    } else {
      // 真机环境：正常下载流程
      this.downloadWechatAvatarOnDevice(avatarUrl);
    }
  }), (0, _defineProperty2.default)(_methods, "handleDevtoolsAvatar", function handleDevtoolsAvatar(avatarUrl) {
    var _this12 = this;
    console.log('开发者工具环境 - 处理微信头像');

    // 在开发者工具中，临时文件可能无法正常访问
    // 我们提供一个备用方案
    uni.hideLoading();
    uni.showModal({
      title: '提示',
      content: '开发者工具中微信头像可能无法正常显示，建议在真机上测试。是否使用其他方式选择头像？',
      confirmText: '从相册选择',
      cancelText: '继续使用',
      success: function success(res) {
        if (res.confirm) {
          _this12.chooseFromAlbum();
        } else {
          // 继续使用微信头像URL，但可能显示异常
          _this12.updateUserAvatar(avatarUrl);
          uni.showToast({
            title: '头像已设置（可能显示异常）',
            icon: 'none'
          });
        }
      }
    });
  }), (0, _defineProperty2.default)(_methods, "downloadWechatAvatarOnDevice", function downloadWechatAvatarOnDevice(avatarUrl) {
    var _this13 = this;
    console.log('真机环境 - 下载微信头像:', avatarUrl);

    // 在真机上正常下载微信头像
    uni.downloadFile({
      url: avatarUrl,
      timeout: 10000,
      // 设置超时时间
      success: function success(res) {
        console.log('下载微信头像成功:', res);
        if (res.statusCode === 200 && res.tempFilePath) {
          // 上传下载的头像文件
          _this13.uploadAvatar(res.tempFilePath);
        } else {
          console.error('下载失败，状态码:', res.statusCode);
          _this13.handleDownloadError('下载头像失败');
        }
      },
      fail: function fail(err) {
        console.error('下载微信头像失败:', err);
        _this13.handleDownloadError('网络错误，下载失败');
      }
    });
  }), (0, _defineProperty2.default)(_methods, "handleDownloadError", function handleDownloadError(message) {
    var _this14 = this;
    uni.hideLoading();
    uni.showModal({
      title: '下载失败',
      content: "".concat(message, "\uFF0C\u662F\u5426\u5C1D\u8BD5\u5176\u4ED6\u65B9\u5F0F\u9009\u62E9\u5934\u50CF\uFF1F"),
      confirmText: '从相册选择',
      cancelText: '取消',
      success: function success(res) {
        if (res.confirm) {
          _this14.chooseFromAlbum();
        }
      }
    });
  }), (0, _defineProperty2.default)(_methods, "onInputNickname", function onInputNickname(e) {
    this.userInfo.nickname = e.detail.value;
  }), (0, _defineProperty2.default)(_methods, "uploadAvatar", function uploadAvatar(filePath) {
    var _this15 = this;
    console.log('开始上传头像:', filePath);

    // 检查文件路径是否有效
    if (!filePath) {
      this.handleUploadError('文件路径无效');
      return;
    }

    // 检查token是否存在
    var token = (0, _auth2.getToken)();
    if (!token) {
      this.handleUploadError('登录状态已过期，请重新登录');
      return;
    }

    // 使用正确的上传端点
    var uploadUrl = this.baseUrl + '/api/system/file/';
    console.log('上传URL:', uploadUrl);
    console.log('Token存在:', !!token);

    // 将头像上传到服务器
    uni.uploadFile({
      url: uploadUrl,
      filePath: filePath,
      name: 'file',
      timeout: 30000,
      // 30秒超时
      header: {
        Authorization: 'JWT ' + token
      },
      success: function success(uploadRes) {
        console.log('上传响应状态码:', uploadRes.statusCode);
        console.log('上传原始响应:', uploadRes);

        // 检查HTTP状态码
        if (uploadRes.statusCode !== 200) {
          _this15.handleUploadError("\u670D\u52A1\u5668\u9519\u8BEF (".concat(uploadRes.statusCode, ")"));
          return;
        }
        try {
          var data = JSON.parse(uploadRes.data);
          console.log('上传解析后响应:', data);
          if (data.code === 2000) {
            var _data$data, _data$data2;
            // 更新头像URL - 尝试多个可能的字段
            var avatarUrl = ((_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.url) || data.url || ((_data$data2 = data.data) === null || _data$data2 === void 0 ? void 0 : _data$data2.file_url);
            console.log('原始头像URL:', avatarUrl);
            if (avatarUrl) {
              // 清理和验证URL
              avatarUrl = _this15.cleanAvatarUrl(avatarUrl);
              console.log('清理后的头像URL:', avatarUrl);
              if (_this15.isValidAvatarUrl(avatarUrl)) {
                _this15.userInfo.avatar = avatarUrl;

                // 同时更新用户信息到后端
                _this15.updateUserAvatar(avatarUrl);
                uni.hideLoading();
                uni.showToast({
                  title: '头像上传成功',
                  icon: 'success'
                });
              } else {
                console.error('头像URL格式无效:', avatarUrl);
                _this15.handleUploadError('头像URL格式无效，请重试');
              }
            } else {
              _this15.handleUploadError('上传成功但获取URL失败');
            }
          } else {
            console.error('上传失败，错误码:', data.code, '错误信息:', data.msg);
            _this15.handleUploadError(data.msg || '上传失败');
          }
        } catch (e) {
          console.error('解析上传响应失败:', e, '原始响应:', uploadRes.data);
          _this15.handleUploadError('服务器响应格式错误');
        }
      },
      fail: function fail(err) {
        console.error('上传请求失败:', err);
        _this15.handleUploadError('网络错误，请检查网络连接');
      }
    });
  }), (0, _defineProperty2.default)(_methods, "cleanAvatarUrl", function cleanAvatarUrl(url) {
    if (!url) return url;

    // 移除可能的特殊字符和编码问题
    var cleanUrl = url.toString().trim();

    // 检查是否包含异常字符
    var hasInvalidChars = /[\x00-\x1F\x7F-\x9F]/.test(cleanUrl);
    if (hasInvalidChars) {
      console.warn('URL包含无效字符，尝试清理:', cleanUrl);
      // 移除控制字符
      cleanUrl = cleanUrl.replace(/[\x00-\x1F\x7F-\x9F]/g, '');
    }

    // 确保URL格式正确
    if (!cleanUrl.startsWith('http') && !cleanUrl.startsWith('/')) {
      // 如果是相对路径，添加基础路径
      if (cleanUrl.startsWith('media/')) {
        cleanUrl = "".concat(this.baseUrl, "/").concat(cleanUrl);
      } else {
        cleanUrl = "".concat(this.baseUrl, "/media/").concat(cleanUrl);
      }
    }
    return cleanUrl;
  }), (0, _defineProperty2.default)(_methods, "isValidAvatarUrl", function isValidAvatarUrl(url) {
    if (!url) return false;

    // 检查URL格式
    try {
      if (url.startsWith('http')) {
        new URL(url);
      }

      // 检查是否包含图片扩展名
      var imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
      var hasImageExt = imageExtensions.some(function (ext) {
        return url.toLowerCase().includes(ext);
      });
      if (!hasImageExt) {
        console.warn('URL不包含图片扩展名:', url);
      }
      return true;
    } catch (e) {
      console.error('URL格式验证失败:', e, url);
      return false;
    }
  }), (0, _defineProperty2.default)(_methods, "handleUploadError", function handleUploadError(message) {
    var _this16 = this;
    uni.hideLoading();
    uni.showModal({
      title: '上传失败',
      content: "".concat(message, "\uFF0C\u662F\u5426\u91CD\u8BD5\u6216\u9009\u62E9\u5176\u4ED6\u65B9\u5F0F\uFF1F"),
      confirmText: '重新选择',
      cancelText: '取消',
      success: function success(res) {
        if (res.confirm) {
          _this16.chooseAvatarImage();
        }
      }
    });
  }), (0, _defineProperty2.default)(_methods, "updateUserAvatar", function updateUserAvatar(avatarUrl) {
    console.log('更新用户头像到后端:', avatarUrl);
    (0, _auth.updateUserInfo)({
      avatar_url: avatarUrl
    }).then(function (res) {
      if (res.code === 2000) {
        // 更新本地存储 - 统一字段名
        var userInfo = uni.getStorageSync('userInfo') || {};
        userInfo.avatarUrl = avatarUrl;
        userInfo.avatar = avatarUrl;
        userInfo.avatar_url = avatarUrl;
        uni.setStorageSync('userInfo', userInfo);
        console.log('头像更新成功，本地存储已更新');
      } else {
        console.error('更新用户头像失败:', res.msg);
      }
    }).catch(function (err) {
      console.error('更新用户头像接口调用失败:', err);
    });
  }), (0, _defineProperty2.default)(_methods, "saveProfile", function saveProfile() {
    var _this17 = this;
    if (!this.userInfo.nickname) {
      return uni.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
    }
    uni.showLoading({
      title: '保存中...'
    });
    var updateData = {
      nickname: this.userInfo.nickname,
      avatar: this.userInfo.avatar
    };
    (0, _auth.updateUserInfo)(updateData).then(function (res) {
      uni.hideLoading();
      if (res.code === 200) {
        // 更新本地存储
        uni.setStorageSync('userInfo', {
          nickName: _this17.userInfo.nickname,
          avatarUrl: _this17.userInfo.avatar,
          gender: _this17.userInfo.gender || 0
        });
        uni.showToast({
          title: '保存成功'
        });

        // 如果是首次登录，跳转到首页，否则返回上一页
        if (_this17.isFirstLogin) {
          _this17.$tab.reLaunch('/pages/index');
        } else {
          // 延迟返回
          setTimeout(function () {
            uni.navigateBack();
          }, 1500);
        }
      } else {
        uni.showToast({
          title: res.msg || '保存失败',
          icon: 'none'
        });
      }
    }).catch(function () {
      uni.hideLoading();
      uni.showToast({
        title: '保存失败',
        icon: 'none'
      });
    });
  }), _methods)
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 112:
/*!***************************************************************************************************************************************!*\
  !*** D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true& ***!
  \***************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_b1b03f2e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../System/java/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true& */ 113);
/* harmony import */ var _System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_b1b03f2e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_b1b03f2e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_b1b03f2e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_b1b03f2e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_System_java_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_System_java_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_System_java_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_System_java_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_b1b03f2e_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 113:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/hanqc/project/pyProject/PlantHome/app/pages/mine/profile/index.vue?vue&type=style&index=0&id=b1b03f2e&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[106,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mine/profile/index.js.map