# 头像功能测试流程

## 测试目标
验证微信一键登录后的头像选择和显示功能是否正常工作。

## 测试步骤

### 1. 微信一键登录测试
- [ ] 打开小程序
- [ ] 点击"微信一键登录"按钮
- [ ] 验证登录成功
- [ ] 检查是否正确跳转到个人资料页面（如果用户没有头像）

### 2. 头像选择测试
- [ ] 在个人资料页面点击头像区域
- [ ] 验证是否弹出选择菜单，包含：
  - [ ] "用微信头像"选项
  - [ ] "从相册选择"选项  
  - [ ] "拍照"选项

### 3. 微信头像选择测试
- [ ] 点击"用微信头像"选项
- [ ] 验证是否显示微信头像选择按钮
- [ ] 点击微信头像选择按钮
- [ ] 验证是否能成功获取微信头像
- [ ] 检查头像是否正确显示在页面上

### 4. 头像显示测试
- [ ] 验证头像在个人资料页面正确显示
- [ ] 检查头像URL是否正确保存到本地存储
- [ ] 验证头像数据是否正确同步到后端

### 5. 字段一致性测试
- [ ] 检查本地存储中的用户信息字段：
  - [ ] `avatar` 字段
  - [ ] `avatarUrl` 字段
  - [ ] `avatar_url` 字段
- [ ] 验证所有字段都包含相同的头像URL

## 预期结果

1. **登录流程**：用户登录后，如果没有头像，应该直接跳转到个人资料页面
2. **头像选择**：点击头像区域应该直接弹出包含微信头像选项的选择菜单
3. **微信头像**：选择微信头像后应该能正确获取并显示
4. **数据一致性**：头像URL应该在所有相关字段中保持一致

## 测试环境

- [ ] 微信开发者工具
- [ ] 真机测试（推荐）

## 注意事项

1. 在开发者工具中，微信头像选择可能会有限制，建议在真机上测试
2. 确保网络连接正常，以便头像能正确加载
3. 检查控制台日志，确认没有错误信息

## 问题排查

如果遇到问题，检查以下几点：

1. **头像不显示**：
   - 检查本地存储中的用户信息
   - 验证头像URL是否有效
   - 确认字段名是否一致

2. **微信头像选择失败**：
   - 检查是否在真机环境
   - 确认微信版本是否支持
   - 查看控制台错误信息

3. **数据同步问题**：
   - 检查网络请求是否成功
   - 验证后端接口是否正常
   - 确认token是否有效
